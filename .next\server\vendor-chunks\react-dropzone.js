"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-dropzone";
exports.ids = ["vendor-chunks/react-dropzone"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-dropzone/dist/es/index.js":
/*!******************************************************!*\
  !*** ./node_modules/react-dropzone/dist/es/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* reexport safe */ _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.ErrorCode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useDropzone: () => (/* binding */ useDropzone)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var file_selector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! file-selector */ \"(ssr)/./node_modules/file-selector/dist/es2015/index.js\");\n/* harmony import */ var _utils_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/index.js */ \"(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js\");\nvar _excluded = [\n    \"children\"\n], _excluded2 = [\n    \"open\"\n], _excluded3 = [\n    \"refKey\",\n    \"role\",\n    \"onKeyDown\",\n    \"onFocus\",\n    \"onBlur\",\n    \"onClick\",\n    \"onDragEnter\",\n    \"onDragOver\",\n    \"onDragLeave\",\n    \"onDrop\"\n], _excluded4 = [\n    \"refKey\",\n    \"onChange\",\n    \"onClick\"\n];\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        enumerableOnly && (symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        })), keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = null != arguments[i] ? arguments[i] : {};\n        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n    }\n    return target;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n/* eslint prefer-template: 0 */ \n\n\n\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */ var Dropzone = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function(_ref, ref) {\n    var children = _ref.children, params = _objectWithoutProperties(_ref, _excluded);\n    var _useDropzone = useDropzone(params), open = _useDropzone.open, props = _objectWithoutProperties(_useDropzone, _excluded2);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, function() {\n        return {\n            open: open\n        };\n    }, [\n        open\n    ]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n        open: open\n    })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\nvar defaultProps = {\n    disabled: false,\n    getFilesFromEvent: file_selector__WEBPACK_IMPORTED_MODULE_1__.fromEvent,\n    maxSize: Infinity,\n    minSize: 0,\n    multiple: true,\n    maxFiles: 0,\n    preventDropOnDocument: true,\n    noClick: false,\n    noKeyboard: false,\n    noDrag: false,\n    noDragEventsBubbling: false,\n    validator: null,\n    useFsAccessApi: false,\n    autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n    /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */ children: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */ accept: prop_types__WEBPACK_IMPORTED_MODULE_3__.objectOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.string)),\n    /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */ multiple: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If false, allow dropped items to take over the current browser window\n   */ preventDropOnDocument: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If true, disables click to open the native file selection dialog\n   */ noClick: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */ noKeyboard: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If true, disables drag 'n' drop\n   */ noDrag: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * If true, stops drag event propagation to parents\n   */ noDragEventsBubbling: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * Minimum file size (in bytes)\n   */ minSize: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n    /**\n   * Maximum file size (in bytes)\n   */ maxSize: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n    /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */ maxFiles: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n    /**\n   * Enable/disable the dropzone\n   */ disabled: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n   */ getFilesFromEvent: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when closing the file dialog with no selection\n   */ onFileDialogCancel: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when opening the file dialog\n   */ onFileDialogOpen: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */ useFsAccessApi: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * Set to true to focus the root element on render\n   */ autoFocus: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n    /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */ onDragEnter: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */ onDragLeave: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */ onDragOver: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */ onDrop: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */ onDropAccepted: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */ onDropRejected: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */ onError: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n    /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */ validator: prop_types__WEBPACK_IMPORTED_MODULE_3__.func\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dropzone);\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */ /**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */ /**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */ /**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */ /**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */ /**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */ /**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */ var initialState = {\n    isFocused: false,\n    isFileDialogActive: false,\n    isDragActive: false,\n    isDragAccept: false,\n    isDragReject: false,\n    acceptedFiles: [],\n    fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */ function useDropzone() {\n    var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props), accept = _defaultProps$props.accept, disabled = _defaultProps$props.disabled, getFilesFromEvent = _defaultProps$props.getFilesFromEvent, maxSize = _defaultProps$props.maxSize, minSize = _defaultProps$props.minSize, multiple = _defaultProps$props.multiple, maxFiles = _defaultProps$props.maxFiles, onDragEnter = _defaultProps$props.onDragEnter, onDragLeave = _defaultProps$props.onDragLeave, onDragOver = _defaultProps$props.onDragOver, onDrop = _defaultProps$props.onDrop, onDropAccepted = _defaultProps$props.onDropAccepted, onDropRejected = _defaultProps$props.onDropRejected, onFileDialogCancel = _defaultProps$props.onFileDialogCancel, onFileDialogOpen = _defaultProps$props.onFileDialogOpen, useFsAccessApi = _defaultProps$props.useFsAccessApi, autoFocus = _defaultProps$props.autoFocus, preventDropOnDocument = _defaultProps$props.preventDropOnDocument, noClick = _defaultProps$props.noClick, noKeyboard = _defaultProps$props.noKeyboard, noDrag = _defaultProps$props.noDrag, noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling, onError = _defaultProps$props.onError, validator = _defaultProps$props.validator;\n    var acceptAttr = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.acceptPropAsAcceptAttr)(accept);\n    }, [\n        accept\n    ]);\n    var pickerTypes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.pickerOptionsFromAccept)(accept);\n    }, [\n        accept\n    ]);\n    var onFileDialogOpenCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n    }, [\n        onFileDialogOpen\n    ]);\n    var onFileDialogCancelCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n    }, [\n        onFileDialogCancel\n    ]);\n    /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */ var rootRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var _useReducer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, initialState), _useReducer2 = _slicedToArray(_useReducer, 2), state = _useReducer2[0], dispatch = _useReducer2[1];\n    var isFocused = state.isFocused, isFileDialogActive = state.isFileDialogActive;\n    var fsAccessApiWorksRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)( false && 0); // Update file dialog active state when the window is focused on\n    var onWindowFocus = function onWindowFocus() {\n        // Execute the timeout only if the file dialog is opened in the browser\n        if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n            setTimeout(function() {\n                if (inputRef.current) {\n                    var files = inputRef.current.files;\n                    if (!files.length) {\n                        dispatch({\n                            type: \"closeDialog\"\n                        });\n                        onFileDialogCancelCb();\n                    }\n                }\n            }, 300);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        window.addEventListener(\"focus\", onWindowFocus, false);\n        return function() {\n            window.removeEventListener(\"focus\", onWindowFocus, false);\n        };\n    }, [\n        inputRef,\n        isFileDialogActive,\n        onFileDialogCancelCb,\n        fsAccessApiWorksRef\n    ]);\n    var dragTargetsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    var onDocumentDrop = function onDocumentDrop(event) {\n        if (rootRef.current && rootRef.current.contains(event.target)) {\n            // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n            return;\n        }\n        event.preventDefault();\n        dragTargetsRef.current = [];\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (preventDropOnDocument) {\n            document.addEventListener(\"dragover\", _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.onDocumentDragOver, false);\n            document.addEventListener(\"drop\", onDocumentDrop, false);\n        }\n        return function() {\n            if (preventDropOnDocument) {\n                document.removeEventListener(\"dragover\", _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.onDocumentDragOver);\n                document.removeEventListener(\"drop\", onDocumentDrop);\n            }\n        };\n    }, [\n        rootRef,\n        preventDropOnDocument\n    ]); // Auto focus the root when autoFocus is true\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!disabled && autoFocus && rootRef.current) {\n            rootRef.current.focus();\n        }\n        return function() {};\n    }, [\n        rootRef,\n        autoFocus,\n        disabled\n    ]);\n    var onErrCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(e) {\n        if (onError) {\n            onError(e);\n        } else {\n            // Let the user know something's gone wrong if they haven't provided the onError cb.\n            console.error(e);\n        }\n    }, [\n        onError\n    ]);\n    var onDragEnterCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n        event.persist();\n        stopPropagation(event);\n        dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [\n            event.target\n        ]);\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event)) {\n            Promise.resolve(getFilesFromEvent(event)).then(function(files) {\n                if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isPropagationStopped)(event) && !noDragEventsBubbling) {\n                    return;\n                }\n                var fileCount = files.length;\n                var isDragAccept = fileCount > 0 && (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.allFilesAccepted)({\n                    files: files,\n                    accept: acceptAttr,\n                    minSize: minSize,\n                    maxSize: maxSize,\n                    multiple: multiple,\n                    maxFiles: maxFiles,\n                    validator: validator\n                });\n                var isDragReject = fileCount > 0 && !isDragAccept;\n                dispatch({\n                    isDragAccept: isDragAccept,\n                    isDragReject: isDragReject,\n                    isDragActive: true,\n                    type: \"setDraggedFiles\"\n                });\n                if (onDragEnter) {\n                    onDragEnter(event);\n                }\n            }).catch(function(e) {\n                return onErrCb(e);\n            });\n        }\n    }, [\n        getFilesFromEvent,\n        onDragEnter,\n        onErrCb,\n        noDragEventsBubbling,\n        acceptAttr,\n        minSize,\n        maxSize,\n        multiple,\n        maxFiles,\n        validator\n    ]);\n    var onDragOverCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.preventDefault();\n        event.persist();\n        stopPropagation(event);\n        var hasFiles = (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event);\n        if (hasFiles && event.dataTransfer) {\n            try {\n                event.dataTransfer.dropEffect = \"copy\";\n            } catch (_unused) {}\n        /* eslint-disable-line no-empty */ }\n        if (hasFiles && onDragOver) {\n            onDragOver(event);\n        }\n        return false;\n    }, [\n        onDragOver,\n        noDragEventsBubbling\n    ]);\n    var onDragLeaveCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.preventDefault();\n        event.persist();\n        stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n        var targets = dragTargetsRef.current.filter(function(target) {\n            return rootRef.current && rootRef.current.contains(target);\n        }); // Make sure to remove a target present multiple times only once\n        // (Firefox may fire dragenter/dragleave multiple times on the same element)\n        var targetIdx = targets.indexOf(event.target);\n        if (targetIdx !== -1) {\n            targets.splice(targetIdx, 1);\n        }\n        dragTargetsRef.current = targets;\n        if (targets.length > 0) {\n            return;\n        }\n        dispatch({\n            type: \"setDraggedFiles\",\n            isDragActive: false,\n            isDragAccept: false,\n            isDragReject: false\n        });\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event) && onDragLeave) {\n            onDragLeave(event);\n        }\n    }, [\n        rootRef,\n        onDragLeave,\n        noDragEventsBubbling\n    ]);\n    var setFiles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(files, event) {\n        var acceptedFiles = [];\n        var fileRejections = [];\n        files.forEach(function(file) {\n            var _fileAccepted = (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.fileAccepted)(file, acceptAttr), _fileAccepted2 = _slicedToArray(_fileAccepted, 2), accepted = _fileAccepted2[0], acceptError = _fileAccepted2[1];\n            var _fileMatchSize = (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.fileMatchSize)(file, minSize, maxSize), _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2), sizeMatch = _fileMatchSize2[0], sizeError = _fileMatchSize2[1];\n            var customErrors = validator ? validator(file) : null;\n            if (accepted && sizeMatch && !customErrors) {\n                acceptedFiles.push(file);\n            } else {\n                var errors = [\n                    acceptError,\n                    sizeError\n                ];\n                if (customErrors) {\n                    errors = errors.concat(customErrors);\n                }\n                fileRejections.push({\n                    file: file,\n                    errors: errors.filter(function(e) {\n                        return e;\n                    })\n                });\n            }\n        });\n        if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n            // Reject everything and empty accepted files\n            acceptedFiles.forEach(function(file) {\n                fileRejections.push({\n                    file: file,\n                    errors: [\n                        _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.TOO_MANY_FILES_REJECTION\n                    ]\n                });\n            });\n            acceptedFiles.splice(0);\n        }\n        dispatch({\n            acceptedFiles: acceptedFiles,\n            fileRejections: fileRejections,\n            isDragReject: fileRejections.length > 0,\n            type: \"setFiles\"\n        });\n        if (onDrop) {\n            onDrop(acceptedFiles, fileRejections, event);\n        }\n        if (fileRejections.length > 0 && onDropRejected) {\n            onDropRejected(fileRejections, event);\n        }\n        if (acceptedFiles.length > 0 && onDropAccepted) {\n            onDropAccepted(acceptedFiles, event);\n        }\n    }, [\n        dispatch,\n        multiple,\n        acceptAttr,\n        minSize,\n        maxSize,\n        maxFiles,\n        onDrop,\n        onDropAccepted,\n        onDropRejected,\n        validator\n    ]);\n    var onDropCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n        event.persist();\n        stopPropagation(event);\n        dragTargetsRef.current = [];\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event)) {\n            Promise.resolve(getFilesFromEvent(event)).then(function(files) {\n                if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isPropagationStopped)(event) && !noDragEventsBubbling) {\n                    return;\n                }\n                setFiles(files, event);\n            }).catch(function(e) {\n                return onErrCb(e);\n            });\n        }\n        dispatch({\n            type: \"reset\"\n        });\n    }, [\n        getFilesFromEvent,\n        setFiles,\n        onErrCb,\n        noDragEventsBubbling\n    ]); // Fn for opening the file dialog programmatically\n    var openFileDialog = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        // No point to use FS access APIs if context is not secure\n        // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n        if (fsAccessApiWorksRef.current) {\n            dispatch({\n                type: \"openDialog\"\n            });\n            onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n            var opts = {\n                multiple: multiple,\n                types: pickerTypes\n            };\n            window.showOpenFilePicker(opts).then(function(handles) {\n                return getFilesFromEvent(handles);\n            }).then(function(files) {\n                setFiles(files, null);\n                dispatch({\n                    type: \"closeDialog\"\n                });\n            }).catch(function(e) {\n                // AbortError means the user canceled\n                if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isAbort)(e)) {\n                    onFileDialogCancelCb(e);\n                    dispatch({\n                        type: \"closeDialog\"\n                    });\n                } else if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isSecurityError)(e)) {\n                    fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n                    // Try using the input\n                    if (inputRef.current) {\n                        inputRef.current.value = null;\n                        inputRef.current.click();\n                    } else {\n                        onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n                    }\n                } else {\n                    onErrCb(e);\n                }\n            });\n            return;\n        }\n        if (inputRef.current) {\n            dispatch({\n                type: \"openDialog\"\n            });\n            onFileDialogOpenCb();\n            inputRef.current.value = null;\n            inputRef.current.click();\n        }\n    }, [\n        dispatch,\n        onFileDialogOpenCb,\n        onFileDialogCancelCb,\n        useFsAccessApi,\n        setFiles,\n        onErrCb,\n        pickerTypes,\n        multiple\n    ]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n    var onKeyDownCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        // Ignore keyboard events bubbling up the DOM tree\n        if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n            return;\n        }\n        if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n            event.preventDefault();\n            openFileDialog();\n        }\n    }, [\n        rootRef,\n        openFileDialog\n    ]); // Update focus state for the dropzone\n    var onFocusCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        dispatch({\n            type: \"focus\"\n        });\n    }, []);\n    var onBlurCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        dispatch({\n            type: \"blur\"\n        });\n    }, []); // Cb to open the file dialog when click occurs on the dropzone\n    var onClickCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if (noClick) {\n            return;\n        } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n        // to ensure React can handle state changes\n        // See: https://github.com/react-dropzone/react-dropzone/issues/450\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isIeOrEdge)()) {\n            setTimeout(openFileDialog, 0);\n        } else {\n            openFileDialog();\n        }\n    }, [\n        noClick,\n        openFileDialog\n    ]);\n    var composeHandler = function composeHandler(fn) {\n        return disabled ? null : fn;\n    };\n    var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n        return noKeyboard ? null : composeHandler(fn);\n    };\n    var composeDragHandler = function composeDragHandler(fn) {\n        return noDrag ? null : composeHandler(fn);\n    };\n    var stopPropagation = function stopPropagation(event) {\n        if (noDragEventsBubbling) {\n            event.stopPropagation();\n        }\n    };\n    var getRootProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return function() {\n            var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, _ref2$refKey = _ref2.refKey, refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey, role = _ref2.role, onKeyDown = _ref2.onKeyDown, onFocus = _ref2.onFocus, onBlur = _ref2.onBlur, onClick = _ref2.onClick, onDragEnter = _ref2.onDragEnter, onDragOver = _ref2.onDragOver, onDragLeave = _ref2.onDragLeave, onDrop = _ref2.onDrop, rest = _objectWithoutProperties(_ref2, _excluded3);\n            return _objectSpread(_objectSpread(_defineProperty({\n                onKeyDown: composeKeyboardHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onKeyDown, onKeyDownCb)),\n                onFocus: composeKeyboardHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onFocus, onFocusCb)),\n                onBlur: composeKeyboardHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onBlur, onBlurCb)),\n                onClick: composeHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onClick, onClickCb)),\n                onDragEnter: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDragEnter, onDragEnterCb)),\n                onDragOver: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDragOver, onDragOverCb)),\n                onDragLeave: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDragLeave, onDragLeaveCb)),\n                onDrop: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDrop, onDropCb)),\n                role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n            }, refKey, rootRef), !disabled && !noKeyboard ? {\n                tabIndex: 0\n            } : {}), rest);\n        };\n    }, [\n        rootRef,\n        onKeyDownCb,\n        onFocusCb,\n        onBlurCb,\n        onClickCb,\n        onDragEnterCb,\n        onDragOverCb,\n        onDragLeaveCb,\n        onDropCb,\n        noKeyboard,\n        noDrag,\n        disabled\n    ]);\n    var onInputElementClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        event.stopPropagation();\n    }, []);\n    var getInputProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function() {\n        return function() {\n            var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}, _ref3$refKey = _ref3.refKey, refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey, onChange = _ref3.onChange, onClick = _ref3.onClick, rest = _objectWithoutProperties(_ref3, _excluded4);\n            var inputProps = _defineProperty({\n                accept: acceptAttr,\n                multiple: multiple,\n                type: \"file\",\n                style: {\n                    border: 0,\n                    clip: \"rect(0, 0, 0, 0)\",\n                    clipPath: \"inset(50%)\",\n                    height: \"1px\",\n                    margin: \"0 -1px -1px 0\",\n                    overflow: \"hidden\",\n                    padding: 0,\n                    position: \"absolute\",\n                    width: \"1px\",\n                    whiteSpace: \"nowrap\"\n                },\n                onChange: composeHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onChange, onDropCb)),\n                onClick: composeHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onClick, onInputElementClick)),\n                tabIndex: -1\n            }, refKey, inputRef);\n            return _objectSpread(_objectSpread({}, inputProps), rest);\n        };\n    }, [\n        inputRef,\n        accept,\n        multiple,\n        onDropCb,\n        disabled\n    ]);\n    return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: isFocused && !disabled,\n        getRootProps: getRootProps,\n        getInputProps: getInputProps,\n        rootRef: rootRef,\n        inputRef: inputRef,\n        open: composeHandler(openFileDialog)\n    });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */ function reducer(state, action) {\n    /* istanbul ignore next */ switch(action.type){\n        case \"focus\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                isFocused: true\n            });\n        case \"blur\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                isFocused: false\n            });\n        case \"openDialog\":\n            return _objectSpread(_objectSpread({}, initialState), {}, {\n                isFileDialogActive: true\n            });\n        case \"closeDialog\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                isFileDialogActive: false\n            });\n        case \"setDraggedFiles\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                isDragActive: action.isDragActive,\n                isDragAccept: action.isDragAccept,\n                isDragReject: action.isDragReject\n            });\n        case \"setFiles\":\n            return _objectSpread(_objectSpread({}, state), {}, {\n                acceptedFiles: action.acceptedFiles,\n                fileRejections: action.fileRejections,\n                isDragReject: action.isDragReject\n            });\n        case \"reset\":\n            return _objectSpread({}, initialState);\n        default:\n            return state;\n    }\n}\nfunction noop() {}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dropzone/dist/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js":
/*!************************************************************!*\
  !*** ./node_modules/react-dropzone/dist/es/utils/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FILE_INVALID_TYPE: () => (/* binding */ FILE_INVALID_TYPE),\n/* harmony export */   FILE_TOO_LARGE: () => (/* binding */ FILE_TOO_LARGE),\n/* harmony export */   FILE_TOO_SMALL: () => (/* binding */ FILE_TOO_SMALL),\n/* harmony export */   TOO_MANY_FILES: () => (/* binding */ TOO_MANY_FILES),\n/* harmony export */   TOO_MANY_FILES_REJECTION: () => (/* binding */ TOO_MANY_FILES_REJECTION),\n/* harmony export */   acceptPropAsAcceptAttr: () => (/* binding */ acceptPropAsAcceptAttr),\n/* harmony export */   allFilesAccepted: () => (/* binding */ allFilesAccepted),\n/* harmony export */   canUseFileSystemAccessAPI: () => (/* binding */ canUseFileSystemAccessAPI),\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers),\n/* harmony export */   fileAccepted: () => (/* binding */ fileAccepted),\n/* harmony export */   fileMatchSize: () => (/* binding */ fileMatchSize),\n/* harmony export */   getInvalidTypeRejectionErr: () => (/* binding */ getInvalidTypeRejectionErr),\n/* harmony export */   getTooLargeRejectionErr: () => (/* binding */ getTooLargeRejectionErr),\n/* harmony export */   getTooSmallRejectionErr: () => (/* binding */ getTooSmallRejectionErr),\n/* harmony export */   isAbort: () => (/* binding */ isAbort),\n/* harmony export */   isEvtWithFiles: () => (/* binding */ isEvtWithFiles),\n/* harmony export */   isExt: () => (/* binding */ isExt),\n/* harmony export */   isIeOrEdge: () => (/* binding */ isIeOrEdge),\n/* harmony export */   isKindFile: () => (/* binding */ isKindFile),\n/* harmony export */   isMIMEType: () => (/* binding */ isMIMEType),\n/* harmony export */   isPropagationStopped: () => (/* binding */ isPropagationStopped),\n/* harmony export */   isSecurityError: () => (/* binding */ isSecurityError),\n/* harmony export */   onDocumentDragOver: () => (/* binding */ onDocumentDragOver),\n/* harmony export */   pickerOptionsFromAccept: () => (/* binding */ pickerOptionsFromAccept)\n/* harmony export */ });\n/* harmony import */ var attr_accept__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! attr-accept */ \"(ssr)/./node_modules/attr-accept/dist/es/index.js\");\nfunction _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        enumerableOnly && (symbols = symbols.filter(function(sym) {\n            return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n        })), keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _objectSpread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = null != arguments[i] ? arguments[i] : {};\n        i % 2 ? ownKeys(Object(source), !0).forEach(function(key) {\n            _defineProperty(target, key, source[key]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function(key) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n    }\n    return target;\n}\nfunction _defineProperty(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _typeof(obj) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(obj) {\n        return typeof obj;\n    } : function(obj) {\n        return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    }, _typeof(obj);\n}\nfunction _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++){\n        arr2[i] = arr[i];\n    }\n    return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n    var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n    if (_i == null) return;\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _s, _e;\n    try {\n        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){\n            _arr.push(_s.value);\n            if (i && _arr.length === i) break;\n        }\n    } catch (err) {\n        _d = true;\n        _e = err;\n    } finally{\n        try {\n            if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n        } finally{\n            if (_d) throw _e;\n        }\n    }\n    return _arr;\n}\nfunction _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n}\n\nvar accepts = typeof attr_accept__WEBPACK_IMPORTED_MODULE_0__ === \"function\" ? attr_accept__WEBPACK_IMPORTED_MODULE_0__ : attr_accept__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; // Error codes\nvar FILE_INVALID_TYPE = \"file-invalid-type\";\nvar FILE_TOO_LARGE = \"file-too-large\";\nvar FILE_TOO_SMALL = \"file-too-small\";\nvar TOO_MANY_FILES = \"too-many-files\";\nvar ErrorCode = {\n    FileInvalidType: FILE_INVALID_TYPE,\n    FileTooLarge: FILE_TOO_LARGE,\n    FileTooSmall: FILE_TOO_SMALL,\n    TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */ var getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n    var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n    var acceptArr = accept.split(\",\");\n    var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n    return {\n        code: FILE_INVALID_TYPE,\n        message: \"File type must be \".concat(msg)\n    };\n};\nvar getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n    return {\n        code: FILE_TOO_LARGE,\n        message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n    };\n};\nvar getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n    return {\n        code: FILE_TOO_SMALL,\n        message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n    };\n};\nvar TOO_MANY_FILES_REJECTION = {\n    code: TOO_MANY_FILES,\n    message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */ function fileAccepted(file, accept) {\n    var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n    return [\n        isAcceptable,\n        isAcceptable ? null : getInvalidTypeRejectionErr(accept)\n    ];\n}\nfunction fileMatchSize(file, minSize, maxSize) {\n    if (isDefined(file.size)) {\n        if (isDefined(minSize) && isDefined(maxSize)) {\n            if (file.size > maxSize) return [\n                false,\n                getTooLargeRejectionErr(maxSize)\n            ];\n            if (file.size < minSize) return [\n                false,\n                getTooSmallRejectionErr(minSize)\n            ];\n        } else if (isDefined(minSize) && file.size < minSize) return [\n            false,\n            getTooSmallRejectionErr(minSize)\n        ];\n        else if (isDefined(maxSize) && file.size > maxSize) return [\n            false,\n            getTooLargeRejectionErr(maxSize)\n        ];\n    }\n    return [\n        true,\n        null\n    ];\n}\nfunction isDefined(value) {\n    return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */ function allFilesAccepted(_ref) {\n    var files = _ref.files, accept = _ref.accept, minSize = _ref.minSize, maxSize = _ref.maxSize, multiple = _ref.multiple, maxFiles = _ref.maxFiles, validator = _ref.validator;\n    if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n        return false;\n    }\n    return files.every(function(file) {\n        var _fileAccepted = fileAccepted(file, accept), _fileAccepted2 = _slicedToArray(_fileAccepted, 1), accepted = _fileAccepted2[0];\n        var _fileMatchSize = fileMatchSize(file, minSize, maxSize), _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1), sizeMatch = _fileMatchSize2[0];\n        var customErrors = validator ? validator(file) : null;\n        return accepted && sizeMatch && !customErrors;\n    });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\nfunction isPropagationStopped(event) {\n    if (typeof event.isPropagationStopped === \"function\") {\n        return event.isPropagationStopped();\n    } else if (typeof event.cancelBubble !== \"undefined\") {\n        return event.cancelBubble;\n    }\n    return false;\n}\nfunction isEvtWithFiles(event) {\n    if (!event.dataTransfer) {\n        return !!event.target && !!event.target.files;\n    } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n    // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n    return Array.prototype.some.call(event.dataTransfer.types, function(type) {\n        return type === \"Files\" || type === \"application/x-moz-file\";\n    });\n}\nfunction isKindFile(item) {\n    return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\nfunction onDocumentDragOver(event) {\n    event.preventDefault();\n}\nfunction isIe(userAgent) {\n    return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\nfunction isEdge(userAgent) {\n    return userAgent.indexOf(\"Edge/\") !== -1;\n}\nfunction isIeOrEdge() {\n    var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n    return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */ function composeEventHandlers() {\n    for(var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++){\n        fns[_key] = arguments[_key];\n    }\n    return function(event) {\n        for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){\n            args[_key2 - 1] = arguments[_key2];\n        }\n        return fns.some(function(fn) {\n            if (!isPropagationStopped(event) && fn) {\n                fn.apply(void 0, [\n                    event\n                ].concat(args));\n            }\n            return isPropagationStopped(event);\n        });\n    };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */ function canUseFileSystemAccessAPI() {\n    return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */ function pickerOptionsFromAccept(accept) {\n    if (isDefined(accept)) {\n        var acceptForPicker = Object.entries(accept).filter(function(_ref2) {\n            var _ref3 = _slicedToArray(_ref2, 2), mimeType = _ref3[0], ext = _ref3[1];\n            var ok = true;\n            if (!isMIMEType(mimeType)) {\n                console.warn('Skipped \"'.concat(mimeType, '\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.'));\n                ok = false;\n            }\n            if (!Array.isArray(ext) || !ext.every(isExt)) {\n                console.warn('Skipped \"'.concat(mimeType, '\" because an invalid file extension was provided.'));\n                ok = false;\n            }\n            return ok;\n        }).reduce(function(agg, _ref4) {\n            var _ref5 = _slicedToArray(_ref4, 2), mimeType = _ref5[0], ext = _ref5[1];\n            return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n        }, {});\n        return [\n            {\n                // description is required due to https://crbug.com/1264708\n                description: \"Files\",\n                accept: acceptForPicker\n            }\n        ];\n    }\n    return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */ function acceptPropAsAcceptAttr(accept) {\n    if (isDefined(accept)) {\n        return Object.entries(accept).reduce(function(a, _ref6) {\n            var _ref7 = _slicedToArray(_ref6, 2), mimeType = _ref7[0], ext = _ref7[1];\n            return [].concat(_toConsumableArray(a), [\n                mimeType\n            ], _toConsumableArray(ext));\n        }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n        .filter(function(v) {\n            return isMIMEType(v) || isExt(v);\n        }).join(\",\");\n    }\n    return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */ function isAbort(v) {\n    return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */ function isSecurityError(v) {\n    return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */ function isMIMEType(v) {\n    return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */ function isExt(v) {\n    return /^.*\\.[\\w]+$/.test(v);\n} /**\n * @typedef {Object.<string, string[]>} AcceptProp\n */  /**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */  /**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js\n");

/***/ })

};
;