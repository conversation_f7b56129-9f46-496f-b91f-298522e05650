'use client'

import { useState } from 'react'
import { User } from 'next-auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar, 
  Plus, 
  Bell, 
  Clock,
  AlertTriangle,
  CheckCircle,
  Trash2,
  Edit,
  Filter
} from 'lucide-react'
import toast from 'react-hot-toast'

interface RemindersContentProps {
  user: User
}

interface Reminder {
  id: string
  title: string
  description?: string
  dueDate: string
  priority: 'low' | 'medium' | 'high'
  isCompleted: boolean
  category: string
}

export function RemindersContent({ user }: RemindersContentProps) {
  const [reminders, setReminders] = useState<Reminder[]>([
    {
      id: '1',
      title: 'Home Insurance Renewal',
      description: 'Review and renew home insurance policy',
      dueDate: '2024-12-15',
      priority: 'high',
      isCompleted: false,
      category: 'Insurance'
    },
    {
      id: '2',
      title: 'Boiler Annual Service',
      description: 'Schedule annual boiler maintenance',
      dueDate: '2024-09-01',
      priority: 'medium',
      isCompleted: false,
      category: 'Maintenance'
    },
    {
      id: '3',
      title: 'Gas Safety Certificate',
      description: 'Renew gas safety certificate',
      dueDate: '2024-08-20',
      priority: 'high',
      isCompleted: true,
      category: 'Compliance'
    }
  ])

  const [showAddForm, setShowAddForm] = useState(false)
  const [newReminder, setNewReminder] = useState({
    title: '',
    description: '',
    dueDate: '',
    priority: 'medium' as 'low' | 'medium' | 'high',
    category: 'General'
  })
  const [filter, setFilter] = useState('all')

  const handleAddReminder = async () => {
    if (!newReminder.title || !newReminder.dueDate) {
      toast.error('Please fill in title and due date')
      return
    }

    try {
      const response = await fetch('/api/reminders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newReminder),
      })

      if (response.ok) {
        const reminder = await response.json()
        setReminders(prev => [reminder, ...prev])
        setNewReminder({
          title: '',
          description: '',
          dueDate: '',
          priority: 'medium',
          category: 'General'
        })
        setShowAddForm(false)
        toast.success('Reminder created successfully!')
      } else {
        toast.error('Failed to create reminder')
      }
    } catch (error) {
      toast.error('Error creating reminder')
    }
  }

  const handleToggleComplete = async (id: string) => {
    try {
      const response = await fetch(`/api/reminders/${id}/toggle`, {
        method: 'PATCH',
      })

      if (response.ok) {
        setReminders(prev => 
          prev.map(reminder => 
            reminder.id === id 
              ? { ...reminder, isCompleted: !reminder.isCompleted }
              : reminder
          )
        )
        toast.success('Reminder updated!')
      } else {
        toast.error('Failed to update reminder')
      }
    } catch (error) {
      toast.error('Error updating reminder')
    }
  }

  const handleDeleteReminder = async (id: string) => {
    if (confirm('Are you sure you want to delete this reminder?')) {
      try {
        const response = await fetch(`/api/reminders/${id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          setReminders(prev => prev.filter(reminder => reminder.id !== id))
          toast.success('Reminder deleted successfully')
        } else {
          toast.error('Failed to delete reminder')
        }
      } catch (error) {
        toast.error('Error deleting reminder')
      }
    }
  }

  const filteredReminders = reminders.filter(reminder => {
    if (filter === 'all') return true
    if (filter === 'pending') return !reminder.isCompleted
    if (filter === 'completed') return reminder.isCompleted
    if (filter === 'overdue') {
      return !reminder.isCompleted && new Date(reminder.dueDate) < new Date()
    }
    return true
  })

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getDaysUntilDue = (dueDate: string) => {
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = due.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Reminders</h1>
              <p className="text-gray-600">Stay on top of important property tasks and deadlines</p>
            </div>
            <Button onClick={() => setShowAddForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Reminder
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total</p>
                  <p className="text-2xl font-bold text-gray-900">{reminders.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reminders.filter(r => !r.isCompleted).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Overdue</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reminders.filter(r => !r.isCompleted && new Date(r.dueDate) < new Date()).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {reminders.filter(r => r.isCompleted).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Add Reminder Form */}
        {showAddForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Add New Reminder</CardTitle>
              <CardDescription>Create a new reminder for important tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={newReminder.title}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="e.g., Renew home insurance"
                  />
                </div>

                <div>
                  <Label htmlFor="dueDate">Due Date</Label>
                  <Input
                    id="dueDate"
                    type="date"
                    value={newReminder.dueDate}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, dueDate: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="priority">Priority</Label>
                  <select
                    id="priority"
                    value={newReminder.priority}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, priority: e.target.value as any }))}
                    className="w-full h-10 px-3 rounded-md border border-input bg-background text-sm"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="category">Category</Label>
                  <select
                    id="category"
                    value={newReminder.category}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full h-10 px-3 rounded-md border border-input bg-background text-sm"
                  >
                    <option value="General">General</option>
                    <option value="Insurance">Insurance</option>
                    <option value="Maintenance">Maintenance</option>
                    <option value="Compliance">Compliance</option>
                    <option value="Legal">Legal</option>
                  </select>
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Input
                    id="description"
                    value={newReminder.description}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Additional details about this reminder"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <Button variant="outline" onClick={() => setShowAddForm(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddReminder}>
                  Create Reminder
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filter Tabs */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex space-x-2">
              {[
                { key: 'all', label: 'All' },
                { key: 'pending', label: 'Pending' },
                { key: 'overdue', label: 'Overdue' },
                { key: 'completed', label: 'Completed' }
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={filter === tab.key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter(tab.key)}
                >
                  {tab.label}
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Reminders List */}
        <Card>
          <CardHeader>
            <CardTitle>Your Reminders ({filteredReminders.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredReminders.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No reminders found</p>
                <p className="text-sm text-gray-500">Create your first reminder to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredReminders.map((reminder) => {
                  const daysUntilDue = getDaysUntilDue(reminder.dueDate)
                  const isOverdue = daysUntilDue < 0 && !reminder.isCompleted
                  
                  return (
                    <div key={reminder.id} className={`border rounded-lg p-4 ${
                      reminder.isCompleted ? 'bg-gray-50' : 'bg-white'
                    } ${isOverdue ? 'border-red-200' : 'border-gray-200'}`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleComplete(reminder.id)}
                            className={reminder.isCompleted ? 'text-green-600' : 'text-gray-400'}
                          >
                            <CheckCircle className="h-5 w-5" />
                          </Button>
                          
                          <div className={reminder.isCompleted ? 'opacity-60' : ''}>
                            <h3 className={`font-semibold ${reminder.isCompleted ? 'line-through' : ''}`}>
                              {reminder.title}
                            </h3>
                            {reminder.description && (
                              <p className="text-sm text-gray-600">{reminder.description}</p>
                            )}
                            <div className="flex items-center space-x-4 mt-2">
                              <Badge className={getPriorityColor(reminder.priority)}>
                                {reminder.priority}
                              </Badge>
                              <Badge variant="secondary">{reminder.category}</Badge>
                              <span className="text-sm text-gray-500">
                                Due: {new Date(reminder.dueDate).toLocaleDateString()}
                              </span>
                              {!reminder.isCompleted && (
                                <span className={`text-sm ${
                                  isOverdue ? 'text-red-600' : daysUntilDue <= 7 ? 'text-yellow-600' : 'text-gray-500'
                                }`}>
                                  {isOverdue 
                                    ? `${Math.abs(daysUntilDue)} days overdue`
                                    : daysUntilDue === 0 
                                    ? 'Due today'
                                    : `${daysUntilDue} days left`
                                  }
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleDeleteReminder(reminder.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
