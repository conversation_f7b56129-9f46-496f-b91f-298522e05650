import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { role } = await request.json()
    
    const userRole = role || 'HOMEOWNER'
    const email = userRole === 'LANDLORD' ? '<EMAIL>' : '<EMAIL>'
    const name = userRole === 'LANDLORD' ? 'Demo Landlord' : 'Demo Homeowner'

    // Check if demo user exists
    let user = await prisma.user.findUnique({
      where: { email }
    })

    // Create demo user if doesn't exist
    if (!user) {
      user = await prisma.user.create({
        data: {
          name,
          email,
          role: userRole,
        }
      })

      // Create a demo property for the user
      await prisma.property.create({
        data: {
          name: userRole === 'LANDLORD' ? 'Demo Property Portfolio' : 'My Home',
          address: userRole === 'LANDLORD' ? '123 Investment Street, London, SW1A 1AA' : '14 Oak Lane, Manchester, M1 1AA',
          type: userRole === 'LANDLORD' ? 'APARTMENT' : 'HOUSE',
          description: userRole === 'LANDLORD' ? 'A sample property in your portfolio' : 'Your beautiful home',
          ownerId: user.id,
          propertyEmail: userRole === 'LANDLORD' ? '<EMAIL>' : '<EMAIL>'
        }
      })
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    })

  } catch (error) {
    console.error('Demo login error:', error)
    return NextResponse.json(
      { message: 'Failed to create demo user' },
      { status: 500 }
    )
  }
}
