import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const reminderId = params.id

    // Find the reminder
    const reminder = await prisma.reminder.findUnique({
      where: { id: reminderId }
    })

    if (!reminder) {
      return NextResponse.json(
        { message: 'Reminder not found' },
        { status: 404 }
      )
    }

    // Check if user owns the reminder
    if (reminder.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Forbidden' },
        { status: 403 }
      )
    }

    // Toggle completion status
    const updatedReminder = await prisma.reminder.update({
      where: { id: reminderId },
      data: { isCompleted: !reminder.isCompleted }
    })

    return NextResponse.json({
      id: updatedReminder.id,
      isCompleted: updatedReminder.isCompleted
    })

  } catch (error) {
    console.error('Toggle reminder error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
