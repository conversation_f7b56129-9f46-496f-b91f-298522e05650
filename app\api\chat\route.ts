import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { message, userId } = await request.json()

    if (!message) {
      return NextResponse.json(
        { message: 'Message is required' },
        { status: 400 }
      )
    }

    // Get user's properties and documents for context
    const properties = await prisma.property.findMany({
      where: { ownerId: session.user.id },
      include: {
        documents: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    })

    // Simple AI response logic (replace with OpenAI API in production)
    let response = generateAIResponse(message.toLowerCase(), properties)

    // Save chat message to database
    await prisma.chatMessage.create({
      data: {
        content: message,
        role: 'user',
        userId: session.user.id,
        propertyId: properties[0]?.id || null
      }
    })

    await prisma.chatMessage.create({
      data: {
        content: response,
        role: 'assistant',
        userId: session.user.id,
        propertyId: properties[0]?.id || null
      }
    })

    return NextResponse.json({ response })

  } catch (error) {
    console.error('Chat error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateAIResponse(message: string, properties: any[]): string {
  const propertyCount = properties.length
  const totalDocuments = properties.reduce((sum, prop) => sum + prop.documents.length, 0)
  
  // Simple keyword-based responses
  if (message.includes('insurance') && message.includes('expire')) {
    return `I found that you have ${totalDocuments} documents across ${propertyCount} properties. Let me check your insurance documents... Based on your records, your home insurance policy expires on December 15, 2024. You should start looking for renewal options soon!`
  }
  
  if (message.includes('gas') && message.includes('certificate')) {
    return `I can see you have gas safety certificates in your documents. Your current gas safety certificate is valid until August 20, 2024. Remember, gas safety certificates need to be renewed annually for rental properties, or every 2 years for your own home.`
  }
  
  if (message.includes('expiring') || message.includes('expire')) {
    return `Looking at your documents, I found 2 items expiring soon:
    
1. Home Insurance Policy - expires December 15, 2024 (⚠️ Expiring soon!)
2. Boiler Warranty - expires March 10, 2025

Would you like me to set reminders for these?`
  }
  
  if (message.includes('reminder')) {
    return `I can help you set reminders! I can create reminders for:
    
• Document renewals (insurance, certificates)
• Maintenance tasks (boiler service, inspections)
• Compliance deadlines
• Property inspections

What would you like to be reminded about?`
  }
  
  if (message.includes('documents') || message.includes('files')) {
    return `You currently have ${totalDocuments} documents stored across ${propertyCount} properties:

• Insurance policies
• Gas safety certificates  
• Warranties and guarantees
• Legal documents
• Maintenance records

Would you like me to help you find a specific document?`
  }
  
  if (message.includes('compliance')) {
    return `For property compliance, you typically need:

✅ Gas Safety Certificate (annual for rentals)
✅ Electrical Installation Certificate (EICR) 
✅ Fire Safety measures
✅ Energy Performance Certificate (EPC)
✅ Legionella risk assessment

I can help track these for you. What type of property compliance are you interested in?`
  }
  
  if (message.includes('hello') || message.includes('hi')) {
    return `Hello! I'm here to help you manage your properties. I can assist with:

• Finding documents and tracking expiry dates
• Setting up reminders for important tasks
• Property compliance requirements
• Maintenance scheduling
• Document organization

What would you like help with today?`
  }
  
  // Default response
  return `I understand you're asking about "${message}". I can help you with property management tasks like:

• Document management and search
• Tracking expiry dates and renewals
• Setting reminders for important tasks
• Property compliance guidance
• Maintenance scheduling

Could you be more specific about what you'd like help with? For example, you could ask "When does my insurance expire?" or "Show me my gas certificates".`
}
