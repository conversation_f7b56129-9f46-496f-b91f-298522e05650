'use client'

import { useState } from 'react'
import { signIn } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Home, Building2, User, ArrowRight, Loader2 } from 'lucide-react'
import toast from 'react-hot-toast'

export default function DemoPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [selectedRole, setSelectedRole] = useState<string | null>(null)
  const router = useRouter()

  const handleDemoLogin = async (role: 'HOMEOWNER' | 'LANDLORD') => {
    setIsLoading(true)
    setSelectedRole(role)

    try {
      // Create demo user
      const response = await fetch('/api/demo-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role }),
      })

      if (response.ok) {
        const data = await response.json()
        
        // Sign in with the demo user
        const result = await signIn('credentials', {
          email: data.user.email,
          password: 'demo', // Demo password
          redirect: false,
        })

        if (result?.ok) {
          toast.success(`Welcome to HomeTwin Demo!`)
          router.push('/dashboard')
        } else {
          toast.error('Demo login failed')
        }
      } else {
        toast.error('Failed to create demo account')
      }
    } catch (error) {
      toast.error('Something went wrong')
    } finally {
      setIsLoading(false)
      setSelectedRole(null)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-2">
              <Home className="h-8 w-8 text-blue-600" />
              <span className="text-2xl font-bold text-gray-900">HomeTwin</span>
            </Link>
            
            <div className="flex items-center space-x-4">
              <Link href="/auth/signin" className="text-gray-600 hover:text-gray-900 transition-colors">
                Sign In
              </Link>
              <Button asChild>
                <Link href="/auth/signup">Get Started</Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Demo Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Try HomeTwin Demo
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Experience HomeTwin's powerful property management features without creating an account. 
            Choose your role to see a personalized demo.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Homeowner Demo */}
          <Card className="border-2 hover:border-blue-300 transition-all duration-300 hover:shadow-lg">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-fit">
                <Home className="h-8 w-8 text-blue-600" />
              </div>
              <CardTitle className="text-2xl">Homeowner Demo</CardTitle>
              <CardDescription className="text-base">
                Manage your home's documents, warranties, and maintenance with AI assistance
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  Property dashboard and overview
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  Document storage and management
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  Smart reminders and alerts
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  AI assistant for property queries
                </div>
              </div>
              
              <Button 
                className="w-full" 
                onClick={() => handleDemoLogin('HOMEOWNER')}
                disabled={isLoading}
              >
                {isLoading && selectedRole === 'HOMEOWNER' ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Starting Demo...
                  </>
                ) : (
                  <>
                    Try Homeowner Demo
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Landlord Demo */}
          <Card className="border-2 hover:border-purple-300 transition-all duration-300 hover:shadow-lg">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto mb-4 p-3 bg-purple-100 rounded-full w-fit">
                <Building2 className="h-8 w-8 text-purple-600" />
              </div>
              <CardTitle className="text-2xl">Landlord Demo</CardTitle>
              <CardDescription className="text-base">
                Scale your property portfolio with compliance tracking and tenant management
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-3 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                  Multi-property portfolio dashboard
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                  Compliance tracking and monitoring
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                  Tenant and contractor access
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                  Portfolio reporting and exports
                </div>
              </div>
              
              <Button 
                className="w-full bg-purple-600 hover:bg-purple-700" 
                onClick={() => handleDemoLogin('LANDLORD')}
                disabled={isLoading}
              >
                {isLoading && selectedRole === 'LANDLORD' ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Starting Demo...
                  </>
                ) : (
                  <>
                    Try Landlord Demo
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="text-center mt-12">
          <p className="text-gray-600 mb-4">
            Ready to get started with your own account?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild variant="outline">
              <Link href="/auth/signup">
                Create Free Account
              </Link>
            </Button>
            <Button asChild variant="ghost">
              <Link href="/">
                Back to Home
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
