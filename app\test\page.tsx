'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'

export default function TestPage() {
  const [testResults, setTestResults] = useState<Record<string, 'pending' | 'success' | 'error'>>({})
  const [testMessages, setTestMessages] = useState<Record<string, string>>({})

  const runTest = async (testName: string, testFn: () => Promise<void>) => {
    setTestResults(prev => ({ ...prev, [testName]: 'pending' }))
    try {
      await testFn()
      setTestResults(prev => ({ ...prev, [testName]: 'success' }))
      setTestMessages(prev => ({ ...prev, [testName]: 'Test passed!' }))
    } catch (error) {
      setTestResults(prev => ({ ...prev, [testName]: 'error' }))
      setTestMessages(prev => ({ ...prev, [testName]: error instanceof Error ? error.message : 'Test failed' }))
    }
  }

  const testDemoLogin = async () => {
    const response = await fetch('/api/demo-login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ role: 'HOMEOWNER' })
    })
    if (!response.ok) throw new Error(`Demo login failed: ${response.status}`)
    const data = await response.json()
    if (!data.success) throw new Error('Demo login returned failure')
  }

  const testUserRegistration = async () => {
    const response = await fetch('/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Test User',
        email: `test-${Date.now()}@example.com`,
        password: 'password123',
        role: 'HOMEOWNER'
      })
    })
    if (!response.ok) throw new Error(`Registration failed: ${response.status}`)
    const data = await response.json()
    if (!data.user) throw new Error('Registration did not return user')
  }

  const testChatAPI = async () => {
    // First create a demo user
    await fetch('/api/demo-login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ role: 'HOMEOWNER' })
    })

    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: 'Hello, when does my insurance expire?',
        userId: 'demo-user'
      })
    })
    if (!response.ok) throw new Error(`Chat API failed: ${response.status}`)
    const data = await response.json()
    if (!data.response) throw new Error('Chat API did not return response')
  }

  const testRemindersAPI = async () => {
    const response = await fetch('/api/reminders', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: 'Test Reminder',
        dueDate: '2024-12-31',
        priority: 'medium',
        category: 'Test'
      })
    })
    if (!response.ok) throw new Error(`Reminders API failed: ${response.status}`)
    const data = await response.json()
    if (!data.id) throw new Error('Reminders API did not return reminder ID')
  }

  const runAllTests = async () => {
    await runTest('Demo Login', testDemoLogin)
    await runTest('User Registration', testUserRegistration)
    await runTest('Chat API', testChatAPI)
    await runTest('Reminders API', testRemindersAPI)
  }

  const getStatusIcon = (status: 'pending' | 'success' | 'error' | undefined) => {
    switch (status) {
      case 'pending': return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />
      default: return null
    }
  }

  const getStatusBadge = (status: 'pending' | 'success' | 'error' | undefined) => {
    switch (status) {
      case 'pending': return <Badge variant="secondary">Running...</Badge>
      case 'success': return <Badge className="bg-green-100 text-green-800">Passed</Badge>
      case 'error': return <Badge variant="destructive">Failed</Badge>
      default: return <Badge variant="outline">Not Run</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">HomeTwin API Tests</h1>
          <p className="text-gray-600">Test the core functionality of the HomeTwin application</p>
        </div>

        <div className="mb-8">
          <Button onClick={runAllTests} size="lg">
            Run All Tests
          </Button>
        </div>

        <div className="grid gap-6">
          {[
            { name: 'Demo Login', description: 'Test demo user creation and login functionality' },
            { name: 'User Registration', description: 'Test new user registration API' },
            { name: 'Chat API', description: 'Test AI chat assistant API' },
            { name: 'Reminders API', description: 'Test reminder creation API' }
          ].map((test) => (
            <Card key={test.name}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(testResults[test.name])}
                    <CardTitle>{test.name}</CardTitle>
                  </div>
                  {getStatusBadge(testResults[test.name])}
                </div>
                <p className="text-sm text-gray-600">{test.description}</p>
              </CardHeader>
              <CardContent>
                {testMessages[test.name] && (
                  <div className={`p-3 rounded-md text-sm ${
                    testResults[test.name] === 'success' 
                      ? 'bg-green-50 text-green-800' 
                      : 'bg-red-50 text-red-800'
                  }`}>
                    {testMessages[test.name]}
                  </div>
                )}
                <div className="mt-4">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      switch (test.name) {
                        case 'Demo Login': runTest('Demo Login', testDemoLogin); break
                        case 'User Registration': runTest('User Registration', testUserRegistration); break
                        case 'Chat API': runTest('Chat API', testChatAPI); break
                        case 'Reminders API': runTest('Reminders API', testRemindersAPI); break
                      }
                    }}
                    disabled={testResults[test.name] === 'pending'}
                  >
                    Run Test
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Manual Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">1. Test Demo Mode</h3>
              <p className="text-sm text-gray-600 mb-2">Go to <a href="/" className="text-blue-600 hover:underline">http://localhost:3000</a> and click "Try Demo"</p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">2. Test Document Upload</h3>
              <p className="text-sm text-gray-600 mb-2">After demo login, go to Documents and try uploading a file</p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">3. Test AI Chat</h3>
              <p className="text-sm text-gray-600 mb-2">Go to Chat and ask: "When does my insurance expire?"</p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-2">4. Test Reminders</h3>
              <p className="text-sm text-gray-600 mb-2">Go to Reminders and create a new reminder</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
