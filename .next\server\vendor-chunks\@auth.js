"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@auth";
exports.ids = ["vendor-chunks/@auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/@auth/prisma-adapter/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@auth/prisma-adapter/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PrismaAdapter: () => (/* binding */ PrismaAdapter)\n/* harmony export */ });\n/**\n * ## Setup\n *\n * Add this adapter to your `auth.ts` Auth.js configuration object:\n *\n * ```js title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import Google from \"next-auth/providers/google\"\n * import { PrismaAdapter } from \"@auth/prisma-adapter\"\n * import { PrismaClient } from \"@prisma/client\"\n *\n * const prisma = new PrismaClient()\n *\n * export const { handlers, auth, signIn, signOut } = NextAuth({\n *   adapter: PrismaAdapter(prisma),\n *   providers: [\n *     Google,\n *   ],\n * })\n * ```\n *\n * ### Create the Prisma schema from scratch\n *\n * You need to use at least Prisma 2.26.0. Create a schema file in `prisma/schema.prisma` similar to this one:\n *\n * > This schema is adapted for use in Prisma and based upon our main [schema](https://authjs.dev/reference/core/adapters#models)\n *\n * ```json title=\"schema.prisma\"\n * datasource db {\n *   provider = \"postgresql\"\n *   url      = env(\"DATABASE_URL\")\n *   shadowDatabaseUrl = env(\"SHADOW_DATABASE_URL\") // Only needed when using a cloud provider that doesn't support the creation of new databases, like Heroku. Learn more: https://pris.ly/d/migrate-shadow\n * }\n *\n * generator client {\n *   provider        = \"prisma-client-js\"\n *   previewFeatures = [\"referentialActions\"] // You won't need this in Prisma 3.X or higher.\n * }\n *\n * model Account {\n *   id                 String  @id @default(cuid())\n *   userId             String\n *   type               String\n *   provider           String\n *   providerAccountId  String\n *   refresh_token      String?  @db.Text\n *   access_token       String?  @db.Text\n *   expires_at         Int?\n *   token_type         String?\n *   scope              String?\n *   id_token           String?  @db.Text\n *   session_state      String?\n *\n *   user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n *\n *   @@unique([provider, providerAccountId])\n * }\n *\n * model Session {\n *   id           String   @id @default(cuid())\n *   sessionToken String   @unique\n *   userId       String\n *   expires      DateTime\n *   user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n * }\n *\n * model User {\n *   id            String    @id @default(cuid())\n *   name          String?\n *   email         String?   @unique\n *   emailVerified DateTime?\n *   image         String?\n *   accounts      Account[]\n *   sessions      Session[]\n * }\n *\n * model VerificationToken {\n *   identifier String\n *   token      String   @unique\n *   expires    DateTime\n *\n *   @@unique([identifier, token])\n * }\n * ```\n *\n * :::note\n * When using the MySQL connector for Prisma, the [Prisma `String` type](https://www.prisma.io/docs/reference/api-reference/prisma-schema-reference#string) gets mapped to `varchar(191)` which may not be long enough to store fields such as `id_token` in the `Account` model. This can be avoided by explicitly using the `Text` type with `@db.Text`.\n * :::\n *\n *\n * ### Create the Prisma schema with `prisma migrate`\n *\n * This will create an SQL migration file and execute it:\n *\n * ```\n * npx prisma migrate dev\n * ```\n *\n * Note that you will need to specify your database connection string in the environment variable `DATABASE_URL`. You can do this by setting it in a `.env` file at the root of your project.\n *\n * To learn more about [Prisma Migrate](https://www.prisma.io/migrate), check out the [Migrate docs](https://www.prisma.io/docs/concepts/components/prisma-migrate).\n *\n * ### Generating the Prisma Client\n *\n * Once you have saved your schema, use the Prisma CLI to generate the Prisma Client:\n *\n * ```\n * npx prisma generate\n * ```\n *\n * To configure your database to use the new schema (i.e. create tables and columns) use the `prisma migrate` command:\n *\n * ```\n * npx prisma migrate dev\n * ```\n *\n * ### MongoDB support\n *\n * Prisma supports MongoDB, and so does Auth.js. Following the instructions of the [Prisma documentation](https://www.prisma.io/docs/concepts/database-connectors/mongodb) on the MongoDB connector, things you have to change are:\n *\n * 1. Make sure that the id fields are mapped correctly\n *\n * ```prisma\n * id  String  @id @default(auto()) @map(\"_id\") @db.ObjectId\n * ```\n *\n * 2. The Native database type attribute to `@db.String` from `@db.Text` and userId to `@db.ObjectId`.\n *\n * ```prisma\n * user_id            String   @db.ObjectId\n * refresh_token      String?  @db.String\n * access_token       String?  @db.String\n * id_token           String?  @db.String\n * ```\n *\n * Everything else should be the same.\n *\n * ### Naming Conventions\n *\n * If mixed snake_case and camelCase column names is an issue for you and/or your underlying database system, we recommend using Prisma's `@map()`([see the documentation here](https://www.prisma.io/docs/concepts/components/prisma-schema/names-in-underlying-database)) feature to change the field names. This won't affect Auth.js, but will allow you to customize the column names to whichever naming convention you wish.\n *\n * For example, moving to `snake_case` and plural table names.\n *\n * ```json title=\"schema.prisma\"\n * model Account {\n *   id                 String  @id @default(cuid())\n *   userId             String  @map(\"user_id\")\n *   type               String\n *   provider           String\n *   providerAccountId  String  @map(\"provider_account_id\")\n *   refresh_token      String? @db.Text\n *   access_token       String? @db.Text\n *   expires_at         Int?\n *   token_type         String?\n *   scope              String?\n *   id_token           String? @db.Text\n *   session_state      String?\n *\n *   user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n *\n *   @@unique([provider, providerAccountId])\n *   @@map(\"accounts\")\n * }\n *\n * model Session {\n *   id           String   @id @default(cuid())\n *   sessionToken String   @unique @map(\"session_token\")\n *   userId       String   @map(\"user_id\")\n *   expires      DateTime\n *   user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n *\n *   @@map(\"sessions\")\n * }\n *\n * model User {\n *   id             String    @id @default(cuid())\n *   name           String?\n *   email          String?   @unique\n *   emailVerified  DateTime? @map(\"email_verified\")\n *   image          String?\n *   accounts       Account[]\n *   sessions       Session[]\n *   authenticators Authenticator[]\n *\n *   @@map(\"users\")\n * }\n *\n * model VerificationToken {\n *   identifier String\n *   token      String   @unique\n *   expires    DateTime\n *\n *   @@unique([identifier, token])\n *   @@map(\"verificationtokens\")\n * }\n *\n * model Authenticator {\n *   id                   String  @id @default(cuid())\n *   credentialID         String  @unique\n *   userId               String\n *   providerAccountId    String\n *   credentialPublicKey  String\n *   counter              Int\n *   credentialDeviceType String\n *   credentialBackedUp   Boolean\n *   transports           String?\n *\n *   user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n * }\n * ```\n *\n **/ function PrismaAdapter(prisma) {\n    const p = prisma;\n    return {\n        // We need to let Prisma generate the ID because our default UUID is incompatible with MongoDB\n        createUser: ({ id: _id, ...data })=>{\n            return p.user.create({\n                data\n            });\n        },\n        getUser: (id)=>p.user.findUnique({\n                where: {\n                    id\n                }\n            }),\n        getUserByEmail: (email)=>p.user.findUnique({\n                where: {\n                    email\n                }\n            }),\n        async getUserByAccount (provider_providerAccountId) {\n            const account = await p.account.findUnique({\n                where: {\n                    provider_providerAccountId\n                },\n                select: {\n                    user: true\n                }\n            });\n            return account?.user ?? null;\n        },\n        updateUser: ({ id, ...data })=>p.user.update({\n                where: {\n                    id\n                },\n                data\n            }),\n        deleteUser: (id)=>p.user.delete({\n                where: {\n                    id\n                }\n            }),\n        linkAccount: (data)=>p.account.create({\n                data\n            }),\n        unlinkAccount: (provider_providerAccountId)=>p.account.delete({\n                where: {\n                    provider_providerAccountId\n                }\n            }),\n        async getSessionAndUser (sessionToken) {\n            const userAndSession = await p.session.findUnique({\n                where: {\n                    sessionToken\n                },\n                include: {\n                    user: true\n                }\n            });\n            if (!userAndSession) return null;\n            const { user, ...session } = userAndSession;\n            return {\n                user,\n                session\n            };\n        },\n        createSession: (data)=>p.session.create({\n                data\n            }),\n        updateSession: (data)=>p.session.update({\n                where: {\n                    sessionToken: data.sessionToken\n                },\n                data\n            }),\n        deleteSession: (sessionToken)=>p.session.delete({\n                where: {\n                    sessionToken\n                }\n            }),\n        async createVerificationToken (data) {\n            const verificationToken = await p.verificationToken.create({\n                data\n            });\n            // @ts-expect-errors // MongoDB needs an ID, but we don't\n            if (verificationToken.id) delete verificationToken.id;\n            return verificationToken;\n        },\n        async useVerificationToken (identifier_token) {\n            try {\n                const verificationToken = await p.verificationToken.delete({\n                    where: {\n                        identifier_token\n                    }\n                });\n                // @ts-expect-errors // MongoDB needs an ID, but we don't\n                if (verificationToken.id) delete verificationToken.id;\n                return verificationToken;\n            } catch (error) {\n                // If token already used/deleted, just return null\n                // https://www.prisma.io/docs/reference/api-reference/error-reference#p2025\n                if (error.code === \"P2025\") return null;\n                throw error;\n            }\n        },\n        async getAccount (providerAccountId, provider) {\n            return p.account.findFirst({\n                where: {\n                    providerAccountId,\n                    provider\n                }\n            });\n        },\n        async createAuthenticator (authenticator) {\n            return p.authenticator.create({\n                data: authenticator\n            }).then(fromDBAuthenticator);\n        },\n        async getAuthenticator (credentialID) {\n            const authenticator = await p.authenticator.findUnique({\n                where: {\n                    credentialID\n                }\n            });\n            return authenticator ? fromDBAuthenticator(authenticator) : null;\n        },\n        async listAuthenticatorsByUserId (userId) {\n            const authenticators = await p.authenticator.findMany({\n                where: {\n                    userId\n                }\n            });\n            return authenticators.map(fromDBAuthenticator);\n        },\n        async updateAuthenticatorCounter (credentialID, counter) {\n            return p.authenticator.update({\n                where: {\n                    credentialID: credentialID\n                },\n                data: {\n                    counter\n                }\n            }).then(fromDBAuthenticator);\n        }\n    };\n}\nfunction fromDBAuthenticator(authenticator) {\n    const { transports, id, user, ...other } = authenticator;\n    return {\n        ...other,\n        transports: transports || undefined\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@auth/prisma-adapter/index.js\n");

/***/ })

};
;