'use client'

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Home, 
  Building2, 
  Bot, 
  FileText, 
  Mail, 
  Calendar, 
  RefreshCw, 
  Shield, 
  Smartphone,
  Zap,
  Users,
  BarChart3,
  CheckCircle,
  ArrowRight,
  Menu,
  X
} from 'lucide-react'

export function LandingPage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const features = [
    {
      icon: Bot,
      title: 'AI Chatbot Assistant',
      description: 'Upload, search, and manage documents via intelligent chat. Ask questions like "When does my boiler warranty expire?"'
    },
    {
      icon: FileText,
      title: 'Smart Document Manager',
      description: 'OCR + AI tagging automatically categorizes your documents by type and tracks expiry dates.'
    },
    {
      icon: Mail,
      title: 'Email Traffic Ingestion',
      description: 'Auto-generated property emails ingest bills, contracts, and certificates automatically.'
    },
    {
      icon: Calendar,
      title: 'Smart Reminders',
      description: 'Never miss important dates. Track insurance, certificates, and warranties with customizable alerts.'
    },
    {
      icon: RefreshCw,
      title: 'Ownership Handover',
      description: 'One-click digital handover of property data when selling or moving home.'
    },
    {
      icon: Zap,
      title: 'IoT Integration',
      description: 'Connect smart home devices via MQTT for live monitoring and automated alerts.'
    }
  ]

  const landlordFeatures = [
    {
      icon: Building2,
      title: 'Multi-Property Dashboard',
      description: 'Manage multiple properties from one interface with filtering and task management.'
    },
    {
      icon: Users,
      title: 'Tenant & Contractor Access',
      description: 'Grant controlled access to tenants and contractors for document viewing and uploads.'
    },
    {
      icon: BarChart3,
      title: 'Compliance Tracker',
      description: 'Dashboard overview of gas safety, fire alarms, EICR, and other compliance requirements.'
    },
    {
      icon: Shield,
      title: 'Portfolio Export',
      description: 'Export all documents for insurance audits or licensing checks across your portfolio.'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Home className="h-8 w-8 text-blue-600" />
              <span className="text-2xl font-bold text-gray-900">HomeTwin</span>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <Link href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">
                Features
              </Link>
              <Link href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">
                Pricing
              </Link>
              <Link href="/auth/signin" className="text-gray-600 hover:text-gray-900 transition-colors">
                Sign In
              </Link>
              <Button asChild>
                <Link href="/auth/signup">Get Started</Link>
              </Button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {mobileMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-200">
              <div className="flex flex-col space-y-4">
                <Link href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Features
                </Link>
                <Link href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Pricing
                </Link>
                <Link href="/auth/signin" className="text-gray-600 hover:text-gray-900 transition-colors">
                  Sign In
                </Link>
                <Button asChild className="w-full">
                  <Link href="/auth/signup">Get Started</Link>
                </Button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <Badge variant="secondary" className="mb-4">
            🚀 Now in Beta - Join Early Access
          </Badge>
          
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Your Property's
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              {' '}Digital Twin
            </span>
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            AI-powered property management for homeowners and landlords. Store documents, 
            track compliance, monitor IoT devices, and manage properties with intelligent automation.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Button size="lg" asChild className="text-lg px-8 py-3">
              <Link href="/auth/signup">
                Start Free Trial <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild className="text-lg px-8 py-3">
              <Link href="/demo">
                Try Demo
              </Link>
            </Button>
          </div>

          {/* User Type Selection */}
          <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            <Card className="border-2 hover:border-blue-300 transition-colors cursor-pointer">
              <CardHeader className="text-center">
                <Home className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <CardTitle>For Homeowners</CardTitle>
                <CardDescription>
                  Manage your home's documents, warranties, and maintenance with AI assistance
                </CardDescription>
              </CardHeader>
            </Card>
            
            <Card className="border-2 hover:border-purple-300 transition-colors cursor-pointer">
              <CardHeader className="text-center">
                <Building2 className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <CardTitle>For Landlords</CardTitle>
                <CardDescription>
                  Scale your property portfolio with compliance tracking and tenant management
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Core Features for Everyone
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Whether you own one home or manage dozens, HomeTwin scales with your needs
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <feature.icon className="h-10 w-10 text-blue-600 mb-4" />
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Landlord Features */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4">
              For Property Managers
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Advanced Tools for Landlords
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Professional features designed for property portfolio management
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {landlordFeatures.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <feature.icon className="h-10 w-10 text-purple-600 mb-4" />
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Transform Your Property Management?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of homeowners and landlords who trust HomeTwin with their properties
          </p>
          <Button size="lg" variant="secondary" asChild className="text-lg px-8 py-3">
            <Link href="/auth/signup">
              Start Your Free Trial <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Home className="h-6 w-6" />
                <span className="text-xl font-bold">HomeTwin</span>
              </div>
              <p className="text-gray-400">
                Your property's intelligent digital memory
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="#features" className="hover:text-white transition-colors">Features</Link></li>
                <li><Link href="#pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><Link href="/docs" className="hover:text-white transition-colors">Documentation</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/about" className="hover:text-white transition-colors">About</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors">Contact</Link></li>
                <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/help" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="/status" className="hover:text-white transition-colors">Status</Link></li>
                <li><Link href="/api" className="hover:text-white transition-colors">API</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 HomeTwin. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
