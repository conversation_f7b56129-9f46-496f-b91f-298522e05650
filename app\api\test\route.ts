import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Test database connection
    const userCount = await prisma.user.count()
    const propertyCount = await prisma.property.count()
    const documentCount = await prisma.document.count()
    const reminderCount = await prisma.reminder.count()

    return NextResponse.json({
      status: 'success',
      message: 'API is working!',
      database: {
        connected: true,
        users: userCount,
        properties: propertyCount,
        documents: documentCount,
        reminders: reminderCount
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Test API error:', error)
    return NextResponse.json({
      status: 'error',
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()

    if (action === 'create-test-user') {
      // Create a test user
      const testUser = await prisma.user.create({
        data: {
          name: 'Test User',
          email: `test-${Date.now()}@example.com`,
          password: 'test123',
          role: 'HOMEOWNER'
        }
      })

      return NextResponse.json({
        status: 'success',
        message: 'Test user created',
        user: {
          id: testUser.id,
          name: testUser.name,
          email: testUser.email,
          role: testUser.role
        }
      })
    }

    if (action === 'cleanup-test-data') {
      // Clean up test data
      await prisma.chatMessage.deleteMany({
        where: {
          user: {
            email: {
              contains: 'test-'
            }
          }
        }
      })

      await prisma.reminder.deleteMany({
        where: {
          user: {
            email: {
              contains: 'test-'
            }
          }
        }
      })

      await prisma.document.deleteMany({
        where: {
          uploadedBy: {
            email: {
              contains: 'test-'
            }
          }
        }
      })

      await prisma.property.deleteMany({
        where: {
          owner: {
            email: {
              contains: 'test-'
            }
          }
        }
      })

      const deletedUsers = await prisma.user.deleteMany({
        where: {
          email: {
            contains: 'test-'
          }
        }
      })

      return NextResponse.json({
        status: 'success',
        message: 'Test data cleaned up',
        deletedUsers: deletedUsers.count
      })
    }

    return NextResponse.json({
      status: 'error',
      message: 'Unknown action'
    }, { status: 400 })

  } catch (error) {
    console.error('Test API POST error:', error)
    return NextResponse.json({
      status: 'error',
      message: 'Test action failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
