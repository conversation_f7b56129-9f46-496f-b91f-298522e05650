import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user's properties and their documents
    const properties = await prisma.property.findMany({
      where: { ownerId: session.user.id },
      include: {
        documents: {
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    // Flatten documents from all properties
    const documents = properties.flatMap(property => 
      property.documents.map(doc => ({
        id: doc.id,
        title: doc.title,
        type: doc.type,
        status: doc.status,
        fileName: doc.fileName,
        fileSize: doc.fileSize,
        mimeType: doc.mimeType,
        fileUrl: doc.fileUrl,
        expiryDate: doc.expiryDate?.toISOString(),
        createdAt: doc.createdAt.toISOString(),
        propertyName: property.name
      }))
    )

    return NextResponse.json({ documents })

  } catch (error) {
    console.error('Documents fetch error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
