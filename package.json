{"name": "hometwin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "setup": "node scripts/setup.js", "postinstall": "prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^1.4.0", "@hookform/resolvers": "^3.3.4", "@prisma/client": "^5.9.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^3.3.1", "framer-motion": "^11.0.3", "lucide-react": "^0.323.0", "next": "14.1.0", "next-auth": "^4.24.6", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "openai": "^4.28.0", "prisma": "^5.9.1", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.49.3", "react-hot-toast": "^2.4.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/eslint": "^8", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}