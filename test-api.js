// Simple test script to verify API functionality
const testAPI = async () => {
  console.log('Testing HomeTwin API...\n');

  // Test 1: Basic API health check
  try {
    const response = await fetch('http://localhost:3000/api/test');
    const data = await response.json();
    console.log('✅ API Health Check:', data);
  } catch (error) {
    console.log('❌ API Health Check failed:', error.message);
  }

  // Test 2: User registration
  try {
    const response = await fetch('http://localhost:3000/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test User',
        email: `test-${Date.now()}@example.com`,
        password: 'password123',
        role: 'HOMEOWNER'
      })
    });
    const data = await response.json();
    console.log('✅ User Registration:', data);
  } catch (error) {
    console.log('❌ User Registration failed:', error.message);
  }

  // Test 3: Demo login
  try {
    const response = await fetch('http://localhost:3000/api/demo-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role: 'HOMEOWNER'
      })
    });
    const data = await response.json();
    console.log('✅ Demo Login:', data);
  } catch (error) {
    console.log('❌ Demo Login failed:', error.message);
  }
};

testAPI();
