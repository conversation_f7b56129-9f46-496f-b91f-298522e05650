// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// SQLite doesn't support enums, so we'll use String fields with constraints in the app

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  password      String? // For credentials authentication
  role          String    @default("HOMEOWNER") // HOMEOWNER, LANDLORD, TENANT, CONTRACTOR
  image         String?
  emailVerified DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts     Account[]
  sessions     Session[]
  properties   Property[]
  documents    Document[]
  reminders    Reminder[]
  chatMessages ChatMessage[]

  // Landlord specific
  tenantAccess     TenantAccess[]
  contractorAccess ContractorAccess[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Property {
  id          String  @id @default(cuid())
  name        String
  address     String
  type        String  @default("HOUSE") // HOUSE, APARTMENT, COMMERCIAL, LAND
  description String?

  // Property email for document ingestion
  propertyEmail String? @unique

  // Owner/Manager
  ownerId String
  owner   User   @relation(fields: [ownerId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  documents         Document[]
  reminders         Reminder[]
  devices           Device[]
  tenantAccess      TenantAccess[]
  contractorAccess  ContractorAccess[]
  chatMessages      ChatMessage[]
  complianceRecords ComplianceRecord[]

  @@map("properties")
}

model Document {
  id          String  @id @default(cuid())
  title       String
  description String?
  type        String  @default("OTHER") // INSURANCE, LEGAL, MAINTENANCE, UTILITIES, CERTIFICATE, WARRANTY, CONTRACT, OTHER
  status      String  @default("ACTIVE") // ACTIVE, EXPIRED, EXPIRING_SOON, ARCHIVED

  // File information
  fileName String
  fileSize Int
  mimeType String
  fileUrl  String

  // OCR and AI extracted data
  extractedText String?
  aiTags        String? // JSON string of tags array

  // Expiry tracking
  expiryDate DateTime?
  issueDate  DateTime?

  // Relations
  propertyId   String
  property     Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  uploadedById String
  uploadedBy   User     @relation(fields: [uploadedById], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("documents")
}

model Reminder {
  id          String   @id @default(cuid())
  title       String
  description String?
  dueDate     DateTime
  isCompleted Boolean  @default(false)

  // Notification settings
  notifyDays String @default("30,7,1") // Comma-separated days before due date to notify

  // Relations
  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  userId     String
  user       User      @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("reminders")
}

model Device {
  id           String  @id @default(cuid())
  name         String
  type         String  @default("OTHER") // BOILER, SMOKE_DETECTOR, LEAK_DETECTOR, SMART_METER, SECURITY_SYSTEM, THERMOSTAT, OTHER
  model        String?
  manufacturer String?

  // MQTT Configuration
  mqttTopic String?
  lastSeen  DateTime?
  isOnline  Boolean   @default(false)

  // Device specific data
  metadata String? // JSON string for device-specific data

  // Relations
  propertyId String
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Device readings/events
  readings DeviceReading[]

  @@map("devices")
}

model DeviceReading {
  id       String @id @default(cuid())
  deviceId String
  device   Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  // Reading data
  value  Float?
  unit   String?
  status String?
  data   String? // JSON string for complex readings

  timestamp DateTime @default(now())

  @@map("device_readings")
}

model TenantAccess {
  id         String   @id @default(cuid())
  propertyId String
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  tenantId   String
  tenant     User     @relation(fields: [tenantId], references: [id])

  // Access permissions
  canViewDocuments  Boolean @default(true)
  canViewCompliance Boolean @default(true)

  createdAt DateTime  @default(now())
  expiresAt DateTime?

  @@unique([propertyId, tenantId])
  @@map("tenant_access")
}

model ContractorAccess {
  id           String   @id @default(cuid())
  propertyId   String
  property     Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  contractorId String
  contractor   User     @relation(fields: [contractorId], references: [id])

  // Access permissions
  canUploadDocuments   Boolean @default(true)
  allowedDocumentTypes String  @default("") // Comma-separated document types

  createdAt DateTime  @default(now())
  expiresAt DateTime?

  @@unique([propertyId, contractorId])
  @@map("contractor_access")
}

model ComplianceRecord {
  id     String @id @default(cuid())
  type   String // GAS_SAFETY, FIRE_ALARM, EICR, EPC, LEGIONELLA, PAT_TESTING
  status String // "compliant", "non_compliant", "pending"

  // Certificate/Record details
  certificateNumber String?
  issueDate         DateTime?
  expiryDate        DateTime?
  issuedBy          String? // Company/person who issued

  // Relations
  propertyId String
  property   Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("compliance_records")
}

model ChatMessage {
  id      String @id @default(cuid())
  content String
  role    String // "user" or "assistant"

  // Context
  propertyId String?
  property   Property? @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  userId     String
  user       User      @relation(fields: [userId], references: [id])

  // AI metadata
  tokens Int?
  model  String?

  createdAt DateTime @default(now())

  @@map("chat_messages")
}

model EmailIngestion {
  id String @id @default(cuid())

  // Email details
  fromEmail String
  toEmail   String // Property email
  subject   String
  body      String

  // Processing status
  processed   Boolean @default(false)
  documentIds String  @default("") // Comma-separated IDs of documents created from this email

  // Metadata
  messageId  String?  @unique
  receivedAt DateTime
  createdAt  DateTime @default(now())

  @@map("email_ingestions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}
