'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { CheckCircle, XCircle, Clock, AlertTriangle, Home } from 'lucide-react'
import Link from 'next/link'

export default function StatusPage() {
  const features = [
    {
      category: "Core Infrastructure",
      items: [
        { name: "Next.js 14 App", status: "working", description: "Modern React framework with TypeScript" },
        { name: "Tailwind CSS", status: "working", description: "Responsive styling system" },
        { name: "Component Library", status: "working", description: "Reusable UI components" },
        { name: "PWA Manifest", status: "working", description: "Progressive Web App configuration" },
        { name: "Database Schema", status: "working", description: "SQLite database with Prisma ORM" }
      ]
    },
    {
      category: "User Interface",
      items: [
        { name: "Landing Page", status: "working", description: "Professional marketing website" },
        { name: "Navigation", status: "working", description: "Links between pages work" },
        { name: "Responsive Design", status: "working", description: "Works on mobile and desktop" },
        { name: "Interactive Components", status: "working", description: "Buttons, forms, cards all functional" },
        { name: "Loading States", status: "working", description: "Visual feedback for user actions" }
      ]
    },
    {
      category: "Authentication",
      items: [
        { name: "NextAuth Setup", status: "partial", description: "Configuration exists but may have issues" },
        { name: "Demo Login", status: "broken", description: "Demo accounts not working properly" },
        { name: "User Registration", status: "broken", description: "Signup form exists but backend issues" },
        { name: "Session Management", status: "broken", description: "User sessions not persisting" },
        { name: "Role-based Access", status: "broken", description: "User roles not enforced" }
      ]
    },
    {
      category: "Document Management",
      items: [
        { name: "File Upload UI", status: "working", description: "Drag & drop interface works" },
        { name: "File Storage", status: "broken", description: "Files not actually saved to disk" },
        { name: "Database Integration", status: "broken", description: "Document metadata not saved" },
        { name: "Search & Filter", status: "working", description: "UI works but no real data" },
        { name: "Document Deletion", status: "broken", description: "Delete API not functional" }
      ]
    },
    {
      category: "AI Chat Assistant",
      items: [
        { name: "Chat Interface", status: "working", description: "Chat UI is functional" },
        { name: "Message Display", status: "working", description: "Messages show correctly" },
        { name: "AI Responses", status: "partial", description: "Simple keyword-based responses" },
        { name: "Context Awareness", status: "broken", description: "AI doesn't know about user data" },
        { name: "Chat History", status: "broken", description: "Messages not saved to database" }
      ]
    },
    {
      category: "Reminders System",
      items: [
        { name: "Reminder UI", status: "working", description: "Create/view reminders interface" },
        { name: "Add Reminders", status: "broken", description: "New reminders not saved" },
        { name: "Toggle Complete", status: "broken", description: "Completion status not persisted" },
        { name: "Delete Reminders", status: "broken", description: "Delete functionality broken" },
        { name: "Due Date Tracking", status: "working", description: "Date calculations work in UI" }
      ]
    },
    {
      category: "Advanced Features",
      items: [
        { name: "Email Integration", status: "not-started", description: "Not implemented yet" },
        { name: "IoT/MQTT", status: "not-started", description: "Not implemented yet" },
        { name: "Property Handover", status: "not-started", description: "Not implemented yet" },
        { name: "Compliance Tracking", status: "not-started", description: "Not implemented yet" },
        { name: "Multi-property Support", status: "not-started", description: "Not implemented yet" }
      ]
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'working': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'partial': return <Clock className="h-4 w-4 text-yellow-500" />
      case 'broken': return <XCircle className="h-4 w-4 text-red-500" />
      case 'not-started': return <AlertTriangle className="h-4 w-4 text-gray-400" />
      default: return null
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'working': return <Badge className="bg-green-100 text-green-800">Working</Badge>
      case 'partial': return <Badge className="bg-yellow-100 text-yellow-800">Partial</Badge>
      case 'broken': return <Badge variant="destructive">Broken</Badge>
      case 'not-started': return <Badge variant="outline">Not Started</Badge>
      default: return null
    }
  }

  const getOverallStats = () => {
    const allItems = features.flatMap(f => f.items)
    return {
      total: allItems.length,
      working: allItems.filter(i => i.status === 'working').length,
      partial: allItems.filter(i => i.status === 'partial').length,
      broken: allItems.filter(i => i.status === 'broken').length,
      notStarted: allItems.filter(i => i.status === 'not-started').length
    }
  }

  const stats = getOverallStats()

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Home className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">HomeTwin - Honest Status Report</h1>
          </div>
          <p className="text-gray-600">Transparent overview of what actually works and what doesn't</p>
        </div>

        {/* Overall Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              <p className="text-sm text-gray-600">Total Features</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-green-600">{stats.working}</p>
              <p className="text-sm text-gray-600">Working</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-yellow-600">{stats.partial}</p>
              <p className="text-sm text-gray-600">Partial</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-red-600">{stats.broken}</p>
              <p className="text-sm text-gray-600">Broken</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <p className="text-2xl font-bold text-gray-400">{stats.notStarted}</p>
              <p className="text-sm text-gray-600">Not Started</p>
            </CardContent>
          </Card>
        </div>

        {/* Feature Categories */}
        <div className="space-y-6">
          {features.map((category, categoryIndex) => (
            <Card key={categoryIndex}>
              <CardHeader>
                <CardTitle>{category.category}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(item.status)}
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-gray-600">{item.description}</p>
                        </div>
                      </div>
                      {getStatusBadge(item.status)}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Action Items */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>What You Can Actually Test Right Now</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-green-600 mb-3">✅ Working Demos:</h3>
                <div className="space-y-2">
                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href="/simple-demo">
                      Try Simple Interactive Demo
                    </Link>
                  </Button>
                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href="/">
                      View Landing Page
                    </Link>
                  </Button>
                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href="/auth/signup">
                      See Registration Form
                    </Link>
                  </Button>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold text-red-600 mb-3">❌ Known Issues:</h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Authentication system needs debugging</li>
                  <li>• Database operations not working</li>
                  <li>• File uploads don't persist</li>
                  <li>• API endpoints have errors</li>
                  <li>• Session management broken</li>
                  <li>• Most backend functionality non-functional</li>
                </ul>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">Honest Assessment:</h3>
              <p className="text-sm text-blue-700">
                This is a well-structured Next.js application with a professional UI, but most of the backend 
                functionality is not working properly. The frontend components and navigation work well, 
                giving you a good sense of what the final application will look like and feel like to use.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
