"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_owhee_Desktop_hometwin_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/chat/route.ts */ \"(rsc)/./app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_owhee_Desktop_hometwin_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/chat/route.ts":
/*!*******************************!*\
  !*** ./app/api/chat/route.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user?.id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { message, userId } = await request.json();\n        if (!message) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"Message is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get user's properties and documents for context\n        const properties = await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.property.findMany({\n            where: {\n                ownerId: session.user.id\n            },\n            include: {\n                documents: {\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 10\n                }\n            }\n        });\n        // Simple AI response logic (replace with OpenAI API in production)\n        let response = generateAIResponse(message.toLowerCase(), properties);\n        // Save chat message to database\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.chatMessage.create({\n            data: {\n                content: message,\n                role: \"user\",\n                userId: session.user.id,\n                propertyId: properties[0]?.id || null\n            }\n        });\n        await _lib_prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.chatMessage.create({\n            data: {\n                content: response,\n                role: \"assistant\",\n                userId: session.user.id,\n                propertyId: properties[0]?.id || null\n            }\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            response\n        });\n    } catch (error) {\n        console.error(\"Chat error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction generateAIResponse(message, properties) {\n    const propertyCount = properties.length;\n    const totalDocuments = properties.reduce((sum, prop)=>sum + prop.documents.length, 0);\n    // Simple keyword-based responses\n    if (message.includes(\"insurance\") && message.includes(\"expire\")) {\n        return `I found that you have ${totalDocuments} documents across ${propertyCount} properties. Let me check your insurance documents... Based on your records, your home insurance policy expires on December 15, 2024. You should start looking for renewal options soon!`;\n    }\n    if (message.includes(\"gas\") && message.includes(\"certificate\")) {\n        return `I can see you have gas safety certificates in your documents. Your current gas safety certificate is valid until August 20, 2024. Remember, gas safety certificates need to be renewed annually for rental properties, or every 2 years for your own home.`;\n    }\n    if (message.includes(\"expiring\") || message.includes(\"expire\")) {\n        return `Looking at your documents, I found 2 items expiring soon:\n    \n1. Home Insurance Policy - expires December 15, 2024 (⚠️ Expiring soon!)\n2. Boiler Warranty - expires March 10, 2025\n\nWould you like me to set reminders for these?`;\n    }\n    if (message.includes(\"reminder\")) {\n        return `I can help you set reminders! I can create reminders for:\n    \n• Document renewals (insurance, certificates)\n• Maintenance tasks (boiler service, inspections)\n• Compliance deadlines\n• Property inspections\n\nWhat would you like to be reminded about?`;\n    }\n    if (message.includes(\"documents\") || message.includes(\"files\")) {\n        return `You currently have ${totalDocuments} documents stored across ${propertyCount} properties:\n\n• Insurance policies\n• Gas safety certificates  \n• Warranties and guarantees\n• Legal documents\n• Maintenance records\n\nWould you like me to help you find a specific document?`;\n    }\n    if (message.includes(\"compliance\")) {\n        return `For property compliance, you typically need:\n\n✅ Gas Safety Certificate (annual for rentals)\n✅ Electrical Installation Certificate (EICR) \n✅ Fire Safety measures\n✅ Energy Performance Certificate (EPC)\n✅ Legionella risk assessment\n\nI can help track these for you. What type of property compliance are you interested in?`;\n    }\n    if (message.includes(\"hello\") || message.includes(\"hi\")) {\n        return `Hello! I'm here to help you manage your properties. I can assist with:\n\n• Finding documents and tracking expiry dates\n• Setting up reminders for important tasks\n• Property compliance requirements\n• Maintenance scheduling\n• Document organization\n\nWhat would you like help with today?`;\n    }\n    // Default response\n    return `I understand you're asking about \"${message}\". I can help you with property management tasks like:\n\n• Document management and search\n• Tracking expiry dates and renewals\n• Setting reminders for important tasks\n• Property compliance guidance\n• Maintenance scheduling\n\nCould you be more specific about what you'd like help with? For example, you could ask \"When does my insurance expire?\" or \"Show me my gas certificates\".`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\nconst authOptions = {\n    session: {\n        strategy: \"jwt\"\n    },\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                // For demo purposes, we'll allow any password\n                // In production, you'd compare hashed passwords\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    image: user.image\n                };\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2hvbWV0d2luLy4vbGliL3ByaXNtYS50cz85ODIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KClcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();