import { getServerSession } from 'next-auth'
import { redirect } from 'next/navigation'
import { authOptions } from '@/lib/auth'
import { RemindersContent } from '@/components/dashboard/reminders-content'

export default async function RemindersPage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/auth/signin')
  }

  return <RemindersContent user={session.user} />
}
