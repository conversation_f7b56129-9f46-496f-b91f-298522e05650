/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/reminders/page";
exports.ids = ["app/dashboard/reminders/page"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Freminders%2Fpage&page=%2Fdashboard%2Freminders%2Fpage&appPaths=%2Fdashboard%2Freminders%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Freminders%2Fpage.tsx&appDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Freminders%2Fpage&page=%2Fdashboard%2Freminders%2Fpage&appPaths=%2Fdashboard%2Freminders%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Freminders%2Fpage.tsx&appDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'reminders',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/reminders/page.tsx */ \"(rsc)/./app/dashboard/reminders/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\dashboard\\\\reminders\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\dashboard\\\\reminders\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/reminders/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/reminders/page\",\n        pathname: \"/dashboard/reminders\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Freminders%2Fpage&page=%2Fdashboard%2Freminders%2Fpage&appPaths=%2Fdashboard%2Freminders%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Freminders%2Fpage.tsx&appDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Ccomponents%5Cdashboard%5Creminders-content.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Ccomponents%5Cdashboard%5Creminders-content.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/dashboard/reminders-content.tsx */ \"(ssr)/./components/dashboard/reminders-content.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDb3doZWUlNUNEZXNrdG9wJTVDaG9tZXR3aW4lNUNjb21wb25lbnRzJTVDZGFzaGJvYXJkJTVDcmVtaW5kZXJzLWNvbnRlbnQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2hvbWV0d2luLz82NTQ3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcb3doZWVcXFxcRGVza3RvcFxcXFxob21ldHdpblxcXFxjb21wb25lbnRzXFxcXGRhc2hib2FyZFxcXFxyZW1pbmRlcnMtY29udGVudC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Ccomponents%5Cdashboard%5Creminders-content.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Ccomponents%5Cproviders.tsx&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Ccomponents%5Cproviders.tsx&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDb3doZWUlNUNEZXNrdG9wJTVDaG9tZXR3aW4lNUNjb21wb25lbnRzJTVDcHJvdmlkZXJzLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q293aGVlJTVDRGVza3RvcCU1Q2hvbWV0d2luJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMmFwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDb3doZWUlNUNEZXNrdG9wJTVDaG9tZXR3aW4lNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q293aGVlJTVDRGVza3RvcCU1Q2hvbWV0d2luJTVDbm9kZV9tb2R1bGVzJTVDcmVhY3QtaG90LXRvYXN0JTVDZGlzdCU1Q2luZGV4Lm1qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQW1HO0FBQ25HIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaG9tZXR3aW4vP2I5NDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxvd2hlZVxcXFxEZXNrdG9wXFxcXGhvbWV0d2luXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcb3doZWVcXFxcRGVza3RvcFxcXFxob21ldHdpblxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaG90LXRvYXN0XFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Ccomponents%5Cproviders.tsx&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/reminders-content.tsx":
/*!****************************************************!*\
  !*** ./components/dashboard/reminders-content.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemindersContent: () => (/* binding */ RemindersContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Plus,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Plus,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Plus,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Plus,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Plus,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Plus,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,CheckCircle,Clock,Edit,Plus,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ RemindersContent auto */ \n\n\n\n\n\n\n\n\nfunction RemindersContent({ user }) {\n    const [reminders, setReminders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            title: \"Home Insurance Renewal\",\n            description: \"Review and renew home insurance policy\",\n            dueDate: \"2024-12-15\",\n            priority: \"high\",\n            isCompleted: false,\n            category: \"Insurance\"\n        },\n        {\n            id: \"2\",\n            title: \"Boiler Annual Service\",\n            description: \"Schedule annual boiler maintenance\",\n            dueDate: \"2024-09-01\",\n            priority: \"medium\",\n            isCompleted: false,\n            category: \"Maintenance\"\n        },\n        {\n            id: \"3\",\n            title: \"Gas Safety Certificate\",\n            description: \"Renew gas safety certificate\",\n            dueDate: \"2024-08-20\",\n            priority: \"high\",\n            isCompleted: true,\n            category: \"Compliance\"\n        }\n    ]);\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newReminder, setNewReminder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        dueDate: \"\",\n        priority: \"medium\",\n        category: \"General\"\n    });\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const handleAddReminder = async ()=>{\n        if (!newReminder.title || !newReminder.dueDate) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Please fill in title and due date\");\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/reminders\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newReminder)\n            });\n            if (response.ok) {\n                const reminder = await response.json();\n                setReminders((prev)=>[\n                        reminder,\n                        ...prev\n                    ]);\n                setNewReminder({\n                    title: \"\",\n                    description: \"\",\n                    dueDate: \"\",\n                    priority: \"medium\",\n                    category: \"General\"\n                });\n                setShowAddForm(false);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Reminder created successfully!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Failed to create reminder\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Error creating reminder\");\n        }\n    };\n    const handleToggleComplete = async (id)=>{\n        try {\n            const response = await fetch(`/api/reminders/${id}/toggle`, {\n                method: \"PATCH\"\n            });\n            if (response.ok) {\n                setReminders((prev)=>prev.map((reminder)=>reminder.id === id ? {\n                            ...reminder,\n                            isCompleted: !reminder.isCompleted\n                        } : reminder));\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Reminder updated!\");\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Failed to update reminder\");\n            }\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Error updating reminder\");\n        }\n    };\n    const handleDeleteReminder = async (id)=>{\n        if (confirm(\"Are you sure you want to delete this reminder?\")) {\n            try {\n                const response = await fetch(`/api/reminders/${id}`, {\n                    method: \"DELETE\"\n                });\n                if (response.ok) {\n                    setReminders((prev)=>prev.filter((reminder)=>reminder.id !== id));\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Reminder deleted successfully\");\n                } else {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Failed to delete reminder\");\n                }\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Error deleting reminder\");\n            }\n        }\n    };\n    const filteredReminders = reminders.filter((reminder)=>{\n        if (filter === \"all\") return true;\n        if (filter === \"pending\") return !reminder.isCompleted;\n        if (filter === \"completed\") return reminder.isCompleted;\n        if (filter === \"overdue\") {\n            return !reminder.isCompleted && new Date(reminder.dueDate) < new Date();\n        }\n        return true;\n    });\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"bg-red-100 text-red-800\";\n            case \"medium\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"low\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getDaysUntilDue = (dueDate)=>{\n        const today = new Date();\n        const due = new Date(dueDate);\n        const diffTime = due.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Reminders\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Stay on top of important property tasks and deadlines\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setShowAddForm(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Reminder\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"Total\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: reminders.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-8 w-8 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"Pending\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: reminders.filter((r)=>!r.isCompleted).length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-8 w-8 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"Overdue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: reminders.filter((r)=>!r.isCompleted && new Date(r.dueDate) < new Date()).length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-600\",\n                                                        children: \"Completed\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: reminders.filter((r)=>r.isCompleted).length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Add New Reminder\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Create a new reminder for important tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"title\",\n                                                        children: \"Title\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"title\",\n                                                        value: newReminder.title,\n                                                        onChange: (e)=>setNewReminder((prev)=>({\n                                                                    ...prev,\n                                                                    title: e.target.value\n                                                                })),\n                                                        placeholder: \"e.g., Renew home insurance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"dueDate\",\n                                                        children: \"Due Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"dueDate\",\n                                                        type: \"date\",\n                                                        value: newReminder.dueDate,\n                                                        onChange: (e)=>setNewReminder((prev)=>({\n                                                                    ...prev,\n                                                                    dueDate: e.target.value\n                                                                }))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"priority\",\n                                                        children: \"Priority\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"priority\",\n                                                        value: newReminder.priority,\n                                                        onChange: (e)=>setNewReminder((prev)=>({\n                                                                    ...prev,\n                                                                    priority: e.target.value\n                                                                })),\n                                                        className: \"w-full h-10 px-3 rounded-md border border-input bg-background text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"low\",\n                                                                children: \"Low\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"medium\",\n                                                                children: \"Medium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"high\",\n                                                                children: \"High\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"category\",\n                                                        children: \"Category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"category\",\n                                                        value: newReminder.category,\n                                                        onChange: (e)=>setNewReminder((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full h-10 px-3 rounded-md border border-input bg-background text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"General\",\n                                                                children: \"General\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Insurance\",\n                                                                children: \"Insurance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Maintenance\",\n                                                                children: \"Maintenance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Compliance\",\n                                                                children: \"Compliance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"Legal\",\n                                                                children: \"Legal\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"description\",\n                                                        children: \"Description (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"description\",\n                                                        value: newReminder.description,\n                                                        onChange: (e)=>setNewReminder((prev)=>({\n                                                                    ...prev,\n                                                                    description: e.target.value\n                                                                })),\n                                                        placeholder: \"Additional details about this reminder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end space-x-2 mt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setShowAddForm(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleAddReminder,\n                                                children: \"Create Reminder\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    {\n                                        key: \"all\",\n                                        label: \"All\"\n                                    },\n                                    {\n                                        key: \"pending\",\n                                        label: \"Pending\"\n                                    },\n                                    {\n                                        key: \"overdue\",\n                                        label: \"Overdue\"\n                                    },\n                                    {\n                                        key: \"completed\",\n                                        label: \"Completed\"\n                                    }\n                                ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: filter === tab.key ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setFilter(tab.key),\n                                        children: tab.label\n                                    }, tab.key, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: [\n                                        \"Your Reminders (\",\n                                        filteredReminders.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: filteredReminders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"No reminders found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Create your first reminder to get started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: filteredReminders.map((reminder)=>{\n                                        const daysUntilDue = getDaysUntilDue(reminder.dueDate);\n                                        const isOverdue = daysUntilDue < 0 && !reminder.isCompleted;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `border rounded-lg p-4 ${reminder.isCompleted ? \"bg-gray-50\" : \"bg-white\"} ${isOverdue ? \"border-red-200\" : \"border-gray-200\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleToggleComplete(reminder.id),\n                                                                className: reminder.isCompleted ? \"text-green-600\" : \"text-gray-400\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: reminder.isCompleted ? \"opacity-60\" : \"\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: `font-semibold ${reminder.isCompleted ? \"line-through\" : \"\"}`,\n                                                                        children: reminder.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    reminder.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: reminder.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-4 mt-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                                className: getPriorityColor(reminder.priority),\n                                                                                children: reminder.priority\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                                variant: \"secondary\",\n                                                                                children: reminder.category\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                                lineNumber: 407,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    \"Due: \",\n                                                                                    new Date(reminder.dueDate).toLocaleDateString()\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            !reminder.isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `text-sm ${isOverdue ? \"text-red-600\" : daysUntilDue <= 7 ? \"text-yellow-600\" : \"text-gray-500\"}`,\n                                                                                children: isOverdue ? `${Math.abs(daysUntilDue)} days overdue` : daysUntilDue === 0 ? \"Due today\" : `${daysUntilDue} days left`\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                variant: \"ghost\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleDeleteReminder(reminder.id),\n                                                                className: \"text-red-600 hover:text-red-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_CheckCircle_Clock_Edit_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                                lineNumber: 431,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, reminder.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\dashboard\\\\reminders-content.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/reminders-content.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\providers.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRWlEO0FBTTFDLFNBQVNDLFVBQVUsRUFBRUMsUUFBUSxFQUFrQjtJQUNwRCxxQkFDRSw4REFBQ0YsNERBQWVBO2tCQUNiRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ob21ldHdpbi8uL2NvbXBvbmVudHMvcHJvdmlkZXJzLnRzeD9jNTYyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFNlc3Npb25Qcm92aWRlcj5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaG9tZXR3aW4vLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9kYTc5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ob21ldHdpbi8uL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4Pzg4ZWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXG4gIFwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCJcbilcblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PiAmXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8TGFiZWxQcmltaXRpdmUuUm9vdFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24obGFiZWxWYXJpYW50cygpLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5MYWJlbC5kaXNwbGF5TmFtZSA9IExhYmVsUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWVcblxuZXhwb3J0IHsgTGFiZWwgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjdmEiLCJjbiIsImxhYmVsVmFyaWFudHMiLCJMYWJlbCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsIlJvb3QiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   extractTextFromFile: () => (/* binding */ extractTextFromFile),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   generateMQTTTopic: () => (/* binding */ generateMQTTTopic),\n/* harmony export */   generatePropertyEmail: () => (/* binding */ generatePropertyEmail),\n/* harmony export */   getDaysUntilExpiry: () => (/* binding */ getDaysUntilExpiry),\n/* harmony export */   getExpiryStatus: () => (/* binding */ getExpiryStatus),\n/* harmony export */   parseDeviceStatus: () => (/* binding */ parseDeviceStatus),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    if (!date) return \"N/A\";\n    const d = typeof date === \"string\" ? new Date(date) : date;\n    return d.toLocaleDateString(\"en-GB\", {\n        day: \"2-digit\",\n        month: \"2-digit\",\n        year: \"numeric\"\n    });\n}\nfunction formatDateTime(date) {\n    if (!date) return \"N/A\";\n    const d = typeof date === \"string\" ? new Date(date) : date;\n    return d.toLocaleString(\"en-GB\", {\n        day: \"2-digit\",\n        month: \"2-digit\",\n        year: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    });\n}\nfunction getDaysUntilExpiry(expiryDate) {\n    if (!expiryDate) return null;\n    const expiry = typeof expiryDate === \"string\" ? new Date(expiryDate) : expiryDate;\n    const today = new Date();\n    const diffTime = expiry.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n}\nfunction getExpiryStatus(expiryDate) {\n    const days = getDaysUntilExpiry(expiryDate);\n    if (days === null) return \"unknown\";\n    if (days < 0) return \"expired\";\n    if (days <= 30) return \"expiring\";\n    return \"active\";\n}\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\nfunction generatePropertyEmail(propertyName) {\n    const slug = propertyName.toLowerCase().replace(/[^a-z0-9\\s-]/g, \"\").replace(/\\s+/g, \"-\").replace(/-+/g, \"-\").trim();\n    return `${slug}@hometwin.app`;\n}\nfunction extractTextFromFile(file) {\n    return new Promise((resolve, reject)=>{\n        if (file.type.startsWith(\"text/\")) {\n            const reader = new FileReader();\n            reader.onload = (e)=>resolve(e.target?.result || \"\");\n            reader.onerror = reject;\n            reader.readAsText(file);\n        } else if (file.type === \"application/pdf\") {\n            // PDF text extraction would require a library like pdf-parse\n            resolve(\"\");\n        } else {\n            resolve(\"\");\n        }\n    });\n}\nfunction validateEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction generateMQTTTopic(propertyId, deviceType, deviceId) {\n    return `property/${propertyId}/${deviceType}/${deviceId}`;\n}\nfunction parseDeviceStatus(status) {\n    const lowerStatus = status.toLowerCase();\n    if (lowerStatus.includes(\"online\") || lowerStatus.includes(\"active\")) return \"online\";\n    if (lowerStatus.includes(\"warning\") || lowerStatus.includes(\"alert\")) return \"warning\";\n    if (lowerStatus.includes(\"error\") || lowerStatus.includes(\"fault\")) return \"error\";\n    return \"offline\";\n}\nfunction debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"491178c1da0d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ob21ldHdpbi8uL2FwcC9nbG9iYWxzLmNzcz8xOWQ0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNDkxMTc4YzFkYTBkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/reminders/page.tsx":
/*!******************************************!*\
  !*** ./app/dashboard/reminders/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RemindersPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _components_dashboard_reminders_content__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/reminders-content */ \"(rsc)/./components/dashboard/reminders-content.tsx\");\n\n\n\n\n\nasync function RemindersPage() {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n    if (!session) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/auth/signin\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_reminders_content__WEBPACK_IMPORTED_MODULE_4__.RemindersContent, {\n        user: session.user\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\dashboard\\\\reminders\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZGFzaGJvYXJkL3JlbWluZGVycy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBNEM7QUFDRjtBQUNGO0FBQ21DO0FBRTVELGVBQWVJO0lBQzVCLE1BQU1DLFVBQVUsTUFBTUwsMkRBQWdCQSxDQUFDRSxrREFBV0E7SUFFbEQsSUFBSSxDQUFDRyxTQUFTO1FBQ1pKLHlEQUFRQSxDQUFDO0lBQ1g7SUFFQSxxQkFBTyw4REFBQ0UscUZBQWdCQTtRQUFDRyxNQUFNRCxRQUFRQyxJQUFJOzs7Ozs7QUFDN0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ob21ldHdpbi8uL2FwcC9kYXNoYm9hcmQvcmVtaW5kZXJzL3BhZ2UudHN4PzJmOTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0U2VydmVyU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aCdcbmltcG9ydCB7IHJlZGlyZWN0IH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tICdAL2xpYi9hdXRoJ1xuaW1wb3J0IHsgUmVtaW5kZXJzQ29udGVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvcmVtaW5kZXJzLWNvbnRlbnQnXG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIFJlbWluZGVyc1BhZ2UoKSB7XG4gIGNvbnN0IHNlc3Npb24gPSBhd2FpdCBnZXRTZXJ2ZXJTZXNzaW9uKGF1dGhPcHRpb25zKVxuXG4gIGlmICghc2Vzc2lvbikge1xuICAgIHJlZGlyZWN0KCcvYXV0aC9zaWduaW4nKVxuICB9XG5cbiAgcmV0dXJuIDxSZW1pbmRlcnNDb250ZW50IHVzZXI9e3Nlc3Npb24udXNlcn0gLz5cbn1cbiJdLCJuYW1lcyI6WyJnZXRTZXJ2ZXJTZXNzaW9uIiwicmVkaXJlY3QiLCJhdXRoT3B0aW9ucyIsIlJlbWluZGVyc0NvbnRlbnQiLCJSZW1pbmRlcnNQYWdlIiwic2Vzc2lvbiIsInVzZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/dashboard/reminders/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"HomeTwin - Your Property's Digital Twin\",\n    description: \"AI-powered property management for homeowners and landlords. Store documents, track compliance, monitor IoT devices, and manage properties with intelligent automation.\",\n    keywords: \"property management, digital twin, AI assistant, document storage, compliance tracking, IoT monitoring, landlord tools, homeowner app\",\n    authors: [\n        {\n            name: \"HomeTwin Team\"\n        }\n    ],\n    creator: \"HomeTwin\",\n    publisher: \"HomeTwin\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    manifest: \"/manifest.json\",\n    themeColor: \"#667eea\",\n    viewport: {\n        width: \"device-width\",\n        initialScale: 1,\n        maximumScale: 1,\n        userScalable: false\n    },\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: \"default\",\n        title: \"HomeTwin\"\n    },\n    icons: {\n        icon: [\n            {\n                url: \"/icon-192x192.png\",\n                sizes: \"192x192\",\n                type: \"image/png\"\n            },\n            {\n                url: \"/icon-512x512.png\",\n                sizes: \"512x512\",\n                type: \"image/png\"\n            }\n        ],\n        apple: [\n            {\n                url: \"/apple-touch-icon.png\",\n                sizes: \"180x180\",\n                type: \"image/png\"\n            }\n        ]\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-title\",\n                        content: \"HomeTwin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#667eea\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-tap-highlight\",\n                        content: \"no\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: \"#363636\",\n                                    color: \"#fff\"\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hometwin\\\\app\\\\layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/dashboard/reminders-content.tsx":
/*!****************************************************!*\
  !*** ./components/dashboard/reminders-content.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RemindersContent: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\hometwin\components\dashboard\reminders-content.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\hometwin\components\dashboard\reminders-content.tsx#RemindersContent`);


/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\hometwin\components\providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\hometwin\components\providers.tsx#Providers`);


/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\nconst authOptions = {\n    session: {\n        strategy: \"jwt\"\n    },\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user) {\n                    return null;\n                }\n                // For demo purposes, we'll allow any password\n                // In production, you'd compare hashed passwords\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    role: user.role,\n                    image: user.image\n                };\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.sub;\n                session.user.role = token.role;\n            }\n            return session;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2hvbWV0d2luLy4vbGliL3ByaXNtYS50cz85ODIyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50J1xuXG5jb25zdCBnbG9iYWxGb3JQcmlzbWEgPSBnbG9iYWxUaGlzIGFzIHVua25vd24gYXMge1xuICBwcmlzbWE6IFByaXNtYUNsaWVudCB8IHVuZGVmaW5lZFxufVxuXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KClcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/openid-client","vendor-chunks/tailwind-merge","vendor-chunks/oauth","vendor-chunks/lucide-react","vendor-chunks/object-hash","vendor-chunks/react-hot-toast","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/uuid","vendor-chunks/@radix-ui","vendor-chunks/oidc-token-hash","vendor-chunks/goober","vendor-chunks/@panva","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Freminders%2Fpage&page=%2Fdashboard%2Freminders%2Fpage&appPaths=%2Fdashboard%2Freminders%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Freminders%2Fpage.tsx&appDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cowhee%5CDesktop%5Chometwin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();