import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { title, description, dueDate, priority, category } = await request.json()

    if (!title || !dueDate) {
      return NextResponse.json(
        { message: 'Title and due date are required' },
        { status: 400 }
      )
    }

    // Get user's first property (for demo purposes)
    let property = await prisma.property.findFirst({
      where: { ownerId: session.user.id }
    })

    // Create property if user doesn't have one
    if (!property) {
      property = await prisma.property.create({
        data: {
          name: 'My Property',
          address: '123 Demo Street',
          type: 'HOUSE',
          ownerId: session.user.id,
        }
      })
    }

    const reminder = await prisma.reminder.create({
      data: {
        title,
        description: description || null,
        dueDate: new Date(dueDate),
        isCompleted: false,
        notifyDays: '30,7,1', // Default notification schedule
        propertyId: property.id,
        userId: session.user.id,
      }
    })

    return NextResponse.json({
      id: reminder.id,
      title: reminder.title,
      description: reminder.description,
      dueDate: reminder.dueDate.toISOString().split('T')[0],
      priority: priority || 'medium',
      isCompleted: reminder.isCompleted,
      category: category || 'General'
    })

  } catch (error) {
    console.error('Create reminder error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const reminders = await prisma.reminder.findMany({
      where: { userId: session.user.id },
      orderBy: { dueDate: 'asc' }
    })

    const formattedReminders = reminders.map(reminder => ({
      id: reminder.id,
      title: reminder.title,
      description: reminder.description,
      dueDate: reminder.dueDate.toISOString().split('T')[0],
      priority: 'medium', // Default since we don't store priority in DB yet
      isCompleted: reminder.isCompleted,
      category: 'General' // Default since we don't store category in DB yet
    }))

    return NextResponse.json(formattedReminders)

  } catch (error) {
    console.error('Get reminders error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
