/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth";
exports.ids = ["vendor-chunks/oauth"];
exports.modules = {

/***/ "(rsc)/./node_modules/oauth/index.js":
/*!*************************************!*\
  !*** ./node_modules/oauth/index.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.OAuth = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuth;\nexports.OAuthEcho = __webpack_require__(/*! ./lib/oauth */ \"(rsc)/./node_modules/oauth/lib/oauth.js\").OAuthEcho;\nexports.OAuth2 = __webpack_require__(/*! ./lib/oauth2 */ \"(rsc)/./node_modules/oauth/lib/oauth2.js\").OAuth2;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUFBLHVHQUE0QztBQUM1Q0EsK0dBQW9EO0FBQ3BEQSwyR0FBK0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ob21ldHdpbi8uL25vZGVfbW9kdWxlcy9vYXV0aC9pbmRleC5qcz81ODk1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydHMuT0F1dGggPSByZXF1aXJlKFwiLi9saWIvb2F1dGhcIikuT0F1dGg7XG5leHBvcnRzLk9BdXRoRWNobyA9IHJlcXVpcmUoXCIuL2xpYi9vYXV0aFwiKS5PQXV0aEVjaG87XG5leHBvcnRzLk9BdXRoMiA9IHJlcXVpcmUoXCIuL2xpYi9vYXV0aDJcIikuT0F1dGgyOyJdLCJuYW1lcyI6WyJleHBvcnRzIiwiT0F1dGgiLCJyZXF1aXJlIiwiT0F1dGhFY2hvIiwiT0F1dGgyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/_utils.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/_utils.js ***!
  \******************************************/
/***/ ((module) => {

"use strict";
eval("// Returns true if this is a host that closes *before* it ends?!?!\n\nmodule.exports.isAnEarlyCloseHost = function(hostName) {\n    return hostName && hostName.match(\".*google(apis)?.com$\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb2F1dGgvbGliL191dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxrRUFBa0U7O0FBQ2xFQSxpQ0FBaUMsR0FBRSxTQUFVRyxRQUFRO0lBQ25ELE9BQU9BLFlBQVlBLFNBQVNDLEtBQUssQ0FBQztBQUNwQyIsInNvdXJjZXMiOlsid2VicGFjazovL2hvbWV0d2luLy4vbm9kZV9tb2R1bGVzL29hdXRoL2xpYi9fdXRpbHMuanM/MTM5MyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBSZXR1cm5zIHRydWUgaWYgdGhpcyBpcyBhIGhvc3QgdGhhdCBjbG9zZXMgKmJlZm9yZSogaXQgZW5kcz8hPyFcbm1vZHVsZS5leHBvcnRzLmlzQW5FYXJseUNsb3NlSG9zdD0gZnVuY3Rpb24oIGhvc3ROYW1lICkge1xuICByZXR1cm4gaG9zdE5hbWUgJiYgaG9zdE5hbWUubWF0Y2goXCIuKmdvb2dsZShhcGlzKT8uY29tJFwiKVxufSJdLCJuYW1lcyI6WyJtb2R1bGUiLCJleHBvcnRzIiwiaXNBbkVhcmx5Q2xvc2VIb3N0IiwiaG9zdE5hbWUiLCJtYXRjaCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/_utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth.js":
/*!*****************************************!*\
  !*** ./node_modules/oauth/lib/oauth.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var crypto = __webpack_require__(/*! crypto */ \"crypto\"), sha1 = __webpack_require__(/*! ./sha1 */ \"(rsc)/./node_modules/oauth/lib/sha1.js\"), http = __webpack_require__(/*! http */ \"http\"), https = __webpack_require__(/*! https */ \"https\"), URL = __webpack_require__(/*! url */ \"url\"), querystring = __webpack_require__(/*! querystring */ \"querystring\"), OAuthUtils = __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth = function(requestUrl, accessUrl, consumerKey, consumerSecret, version, authorize_callback, signatureMethod, nonceSize, customHeaders) {\n    this._isEcho = false;\n    this._requestUrl = requestUrl;\n    this._accessUrl = accessUrl;\n    this._consumerKey = consumerKey;\n    this._consumerSecret = this._encodeData(consumerSecret);\n    if (signatureMethod == \"RSA-SHA1\") {\n        this._privateKey = consumerSecret;\n    }\n    this._version = version;\n    if (authorize_callback === undefined) {\n        this._authorize_callback = \"oob\";\n    } else {\n        this._authorize_callback = authorize_callback;\n    }\n    if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n    this._signatureMethod = signatureMethod;\n    this._nonceSize = nonceSize || 32;\n    this._headers = customHeaders || {\n        \"Accept\": \"*/*\",\n        \"Connection\": \"close\",\n        \"User-Agent\": \"Node authentication\"\n    };\n    this._clientOptions = this._defaultClientOptions = {\n        \"requestTokenHttpMethod\": \"POST\",\n        \"accessTokenHttpMethod\": \"POST\",\n        \"followRedirects\": true\n    };\n    this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho = function(realm, verify_credentials, consumerKey, consumerSecret, version, signatureMethod, nonceSize, customHeaders) {\n    this._isEcho = true;\n    this._realm = realm;\n    this._verifyCredentials = verify_credentials;\n    this._consumerKey = consumerKey;\n    this._consumerSecret = this._encodeData(consumerSecret);\n    if (signatureMethod == \"RSA-SHA1\") {\n        this._privateKey = consumerSecret;\n    }\n    this._version = version;\n    if (signatureMethod != \"PLAINTEXT\" && signatureMethod != \"HMAC-SHA1\" && signatureMethod != \"RSA-SHA1\") throw new Error(\"Un-supported signature method: \" + signatureMethod);\n    this._signatureMethod = signatureMethod;\n    this._nonceSize = nonceSize || 32;\n    this._headers = customHeaders || {\n        \"Accept\": \"*/*\",\n        \"Connection\": \"close\",\n        \"User-Agent\": \"Node authentication\"\n    };\n    this._oauthParameterSeperator = \",\";\n};\nexports.OAuthEcho.prototype = exports.OAuth.prototype;\nexports.OAuth.prototype._getTimestamp = function() {\n    return Math.floor(new Date().getTime() / 1000);\n};\nexports.OAuth.prototype._encodeData = function(toEncode) {\n    if (toEncode == null || toEncode == \"\") return \"\";\n    else {\n        var result = encodeURIComponent(toEncode);\n        // Fix the mismatch between OAuth's  RFC3986's and Javascript's beliefs in what is right and wrong ;)\n        return result.replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n    }\n};\nexports.OAuth.prototype._decodeData = function(toDecode) {\n    if (toDecode != null) {\n        toDecode = toDecode.replace(/\\+/g, \" \");\n    }\n    return decodeURIComponent(toDecode);\n};\nexports.OAuth.prototype._getSignature = function(method, url, parameters, tokenSecret) {\n    var signatureBase = this._createSignatureBase(method, url, parameters);\n    return this._createSignature(signatureBase, tokenSecret);\n};\nexports.OAuth.prototype._normalizeUrl = function(url) {\n    var parsedUrl = URL.parse(url, true);\n    var port = \"\";\n    if (parsedUrl.port) {\n        if (parsedUrl.protocol == \"http:\" && parsedUrl.port != \"80\" || parsedUrl.protocol == \"https:\" && parsedUrl.port != \"443\") {\n            port = \":\" + parsedUrl.port;\n        }\n    }\n    if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n    return parsedUrl.protocol + \"//\" + parsedUrl.hostname + port + parsedUrl.pathname;\n};\n// Is the parameter considered an OAuth parameter\nexports.OAuth.prototype._isParameterNameAnOAuthParameter = function(parameter) {\n    var m = parameter.match(\"^oauth_\");\n    if (m && m[0] === \"oauth_\") {\n        return true;\n    } else {\n        return false;\n    }\n};\n// build the OAuth request authorization header\nexports.OAuth.prototype._buildAuthorizationHeaders = function(orderedParameters) {\n    var authHeader = \"OAuth \";\n    if (this._isEcho) {\n        authHeader += 'realm=\"' + this._realm + '\",';\n    }\n    for(var i = 0; i < orderedParameters.length; i++){\n        // Whilst the all the parameters should be included within the signature, only the oauth_ arguments\n        // should appear within the authorization header.\n        if (this._isParameterNameAnOAuthParameter(orderedParameters[i][0])) {\n            authHeader += \"\" + this._encodeData(orderedParameters[i][0]) + '=\"' + this._encodeData(orderedParameters[i][1]) + '\"' + this._oauthParameterSeperator;\n        }\n    }\n    authHeader = authHeader.substring(0, authHeader.length - this._oauthParameterSeperator.length);\n    return authHeader;\n};\n// Takes an object literal that represents the arguments, and returns an array\n// of argument/value pairs.\nexports.OAuth.prototype._makeArrayOfArgumentsHash = function(argumentsHash) {\n    var argument_pairs = [];\n    for(var key in argumentsHash){\n        if (argumentsHash.hasOwnProperty(key)) {\n            var value = argumentsHash[key];\n            if (Array.isArray(value)) {\n                for(var i = 0; i < value.length; i++){\n                    argument_pairs[argument_pairs.length] = [\n                        key,\n                        value[i]\n                    ];\n                }\n            } else {\n                argument_pairs[argument_pairs.length] = [\n                    key,\n                    value\n                ];\n            }\n        }\n    }\n    return argument_pairs;\n};\n// Sorts the encoded key value pairs by encoded name, then encoded value\nexports.OAuth.prototype._sortRequestParams = function(argument_pairs) {\n    // Sort by name, then value.\n    argument_pairs.sort(function(a, b) {\n        if (a[0] == b[0]) {\n            return a[1] < b[1] ? -1 : 1;\n        } else return a[0] < b[0] ? -1 : 1;\n    });\n    return argument_pairs;\n};\nexports.OAuth.prototype._normaliseRequestParams = function(args) {\n    var argument_pairs = this._makeArrayOfArgumentsHash(args);\n    // First encode them #3.4.1.3.2 .1\n    for(var i = 0; i < argument_pairs.length; i++){\n        argument_pairs[i][0] = this._encodeData(argument_pairs[i][0]);\n        argument_pairs[i][1] = this._encodeData(argument_pairs[i][1]);\n    }\n    // Then sort them #3.4.1.3.2 .2\n    argument_pairs = this._sortRequestParams(argument_pairs);\n    // Then concatenate together #3.4.1.3.2 .3 & .4\n    var args = \"\";\n    for(var i = 0; i < argument_pairs.length; i++){\n        args += argument_pairs[i][0];\n        args += \"=\";\n        args += argument_pairs[i][1];\n        if (i < argument_pairs.length - 1) args += \"&\";\n    }\n    return args;\n};\nexports.OAuth.prototype._createSignatureBase = function(method, url, parameters) {\n    url = this._encodeData(this._normalizeUrl(url));\n    parameters = this._encodeData(parameters);\n    return method.toUpperCase() + \"&\" + url + \"&\" + parameters;\n};\nexports.OAuth.prototype._createSignature = function(signatureBase, tokenSecret) {\n    if (tokenSecret === undefined) var tokenSecret = \"\";\n    else tokenSecret = this._encodeData(tokenSecret);\n    // consumerSecret is already encoded\n    var key = this._consumerSecret + \"&\" + tokenSecret;\n    var hash = \"\";\n    if (this._signatureMethod == \"PLAINTEXT\") {\n        hash = key;\n    } else if (this._signatureMethod == \"RSA-SHA1\") {\n        key = this._privateKey || \"\";\n        hash = crypto.createSign(\"RSA-SHA1\").update(signatureBase).sign(key, \"base64\");\n    } else {\n        if (crypto.Hmac) {\n            hash = crypto.createHmac(\"sha1\", key).update(signatureBase).digest(\"base64\");\n        } else {\n            hash = sha1.HMACSHA1(key, signatureBase);\n        }\n    }\n    return hash;\n};\nexports.OAuth.prototype.NONCE_CHARS = [\n    \"a\",\n    \"b\",\n    \"c\",\n    \"d\",\n    \"e\",\n    \"f\",\n    \"g\",\n    \"h\",\n    \"i\",\n    \"j\",\n    \"k\",\n    \"l\",\n    \"m\",\n    \"n\",\n    \"o\",\n    \"p\",\n    \"q\",\n    \"r\",\n    \"s\",\n    \"t\",\n    \"u\",\n    \"v\",\n    \"w\",\n    \"x\",\n    \"y\",\n    \"z\",\n    \"A\",\n    \"B\",\n    \"C\",\n    \"D\",\n    \"E\",\n    \"F\",\n    \"G\",\n    \"H\",\n    \"I\",\n    \"J\",\n    \"K\",\n    \"L\",\n    \"M\",\n    \"N\",\n    \"O\",\n    \"P\",\n    \"Q\",\n    \"R\",\n    \"S\",\n    \"T\",\n    \"U\",\n    \"V\",\n    \"W\",\n    \"X\",\n    \"Y\",\n    \"Z\",\n    \"0\",\n    \"1\",\n    \"2\",\n    \"3\",\n    \"4\",\n    \"5\",\n    \"6\",\n    \"7\",\n    \"8\",\n    \"9\"\n];\nexports.OAuth.prototype._getNonce = function(nonceSize) {\n    var result = [];\n    var chars = this.NONCE_CHARS;\n    var char_pos;\n    var nonce_chars_length = chars.length;\n    for(var i = 0; i < nonceSize; i++){\n        char_pos = Math.floor(Math.random() * nonce_chars_length);\n        result[i] = chars[char_pos];\n    }\n    return result.join(\"\");\n};\nexports.OAuth.prototype._createClient = function(port, hostname, method, path, headers, sslEnabled) {\n    var options = {\n        host: hostname,\n        port: port,\n        path: path,\n        method: method,\n        headers: headers\n    };\n    var httpModel;\n    if (sslEnabled) {\n        httpModel = https;\n    } else {\n        httpModel = http;\n    }\n    return httpModel.request(options);\n};\nexports.OAuth.prototype._prepareParameters = function(oauth_token, oauth_token_secret, method, url, extra_params) {\n    var oauthParameters = {\n        \"oauth_timestamp\": this._getTimestamp(),\n        \"oauth_nonce\": this._getNonce(this._nonceSize),\n        \"oauth_version\": this._version,\n        \"oauth_signature_method\": this._signatureMethod,\n        \"oauth_consumer_key\": this._consumerKey\n    };\n    if (oauth_token) {\n        oauthParameters[\"oauth_token\"] = oauth_token;\n    }\n    var sig;\n    if (this._isEcho) {\n        sig = this._getSignature(\"GET\", this._verifyCredentials, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n    } else {\n        if (extra_params) {\n            for(var key in extra_params){\n                if (extra_params.hasOwnProperty(key)) oauthParameters[key] = extra_params[key];\n            }\n        }\n        var parsedUrl = URL.parse(url, false);\n        if (parsedUrl.query) {\n            var key2;\n            var extraParameters = querystring.parse(parsedUrl.query);\n            for(var key in extraParameters){\n                var value = extraParameters[key];\n                if (typeof value == \"object\") {\n                    // TODO: This probably should be recursive\n                    for(key2 in value){\n                        oauthParameters[key + \"[\" + key2 + \"]\"] = value[key2];\n                    }\n                } else {\n                    oauthParameters[key] = value;\n                }\n            }\n        }\n        sig = this._getSignature(method, url, this._normaliseRequestParams(oauthParameters), oauth_token_secret);\n    }\n    var orderedParameters = this._sortRequestParams(this._makeArrayOfArgumentsHash(oauthParameters));\n    orderedParameters[orderedParameters.length] = [\n        \"oauth_signature\",\n        sig\n    ];\n    return orderedParameters;\n};\nexports.OAuth.prototype._performSecureRequest = function(oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback) {\n    var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, extra_params);\n    if (!post_content_type) {\n        post_content_type = \"application/x-www-form-urlencoded\";\n    }\n    var parsedUrl = URL.parse(url, false);\n    if (parsedUrl.protocol == \"http:\" && !parsedUrl.port) parsedUrl.port = 80;\n    if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) parsedUrl.port = 443;\n    var headers = {};\n    var authorization = this._buildAuthorizationHeaders(orderedParameters);\n    if (this._isEcho) {\n        headers[\"X-Verify-Credentials-Authorization\"] = authorization;\n    } else {\n        headers[\"Authorization\"] = authorization;\n    }\n    headers[\"Host\"] = parsedUrl.host;\n    for(var key in this._headers){\n        if (this._headers.hasOwnProperty(key)) {\n            headers[key] = this._headers[key];\n        }\n    }\n    // Filter out any passed extra_params that are really to do with OAuth\n    for(var key in extra_params){\n        if (this._isParameterNameAnOAuthParameter(key)) {\n            delete extra_params[key];\n        }\n    }\n    if ((method == \"POST\" || method == \"PUT\") && post_body == null && extra_params != null) {\n        // Fix the mismatch between the output of querystring.stringify() and this._encodeData()\n        post_body = querystring.stringify(extra_params).replace(/\\!/g, \"%21\").replace(/\\'/g, \"%27\").replace(/\\(/g, \"%28\").replace(/\\)/g, \"%29\").replace(/\\*/g, \"%2A\");\n    }\n    if (post_body) {\n        if (Buffer.isBuffer(post_body)) {\n            headers[\"Content-length\"] = post_body.length;\n        } else {\n            headers[\"Content-length\"] = Buffer.byteLength(post_body);\n        }\n    } else {\n        headers[\"Content-length\"] = 0;\n    }\n    headers[\"Content-Type\"] = post_content_type;\n    var path;\n    if (!parsedUrl.pathname || parsedUrl.pathname == \"\") parsedUrl.pathname = \"/\";\n    if (parsedUrl.query) path = parsedUrl.pathname + \"?\" + parsedUrl.query;\n    else path = parsedUrl.pathname;\n    var request;\n    if (parsedUrl.protocol == \"https:\") {\n        request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers, true);\n    } else {\n        request = this._createClient(parsedUrl.port, parsedUrl.hostname, method, path, headers);\n    }\n    var clientOptions = this._clientOptions;\n    if (callback) {\n        var data = \"\";\n        var self = this;\n        // Some hosts *cough* google appear to close the connection early / send no content-length header\n        // allow this behaviour.\n        var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(parsedUrl.hostname);\n        var callbackCalled = false;\n        var passBackControl = function(response) {\n            if (!callbackCalled) {\n                callbackCalled = true;\n                if (response.statusCode >= 200 && response.statusCode <= 299) {\n                    callback(null, data, response);\n                } else {\n                    // Follow 301 or 302 redirects with Location HTTP header\n                    if ((response.statusCode == 301 || response.statusCode == 302) && clientOptions.followRedirects && response.headers && response.headers.location) {\n                        self._performSecureRequest(oauth_token, oauth_token_secret, method, response.headers.location, extra_params, post_body, post_content_type, callback);\n                    } else {\n                        callback({\n                            statusCode: response.statusCode,\n                            data: data\n                        }, data, response);\n                    }\n                }\n            }\n        };\n        request.on(\"response\", function(response) {\n            response.setEncoding(\"utf8\");\n            response.on(\"data\", function(chunk) {\n                data += chunk;\n            });\n            response.on(\"end\", function() {\n                passBackControl(response);\n            });\n            response.on(\"close\", function() {\n                if (allowEarlyClose) {\n                    passBackControl(response);\n                }\n            });\n        });\n        request.on(\"error\", function(err) {\n            if (!callbackCalled) {\n                callbackCalled = true;\n                callback(err);\n            }\n        });\n        if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n            request.write(post_body);\n        }\n        request.end();\n    } else {\n        if ((method == \"POST\" || method == \"PUT\") && post_body != null && post_body != \"\") {\n            request.write(post_body);\n        }\n        return request;\n    }\n    return;\n};\nexports.OAuth.prototype.setClientOptions = function(options) {\n    var key, mergedOptions = {}, hasOwnProperty = Object.prototype.hasOwnProperty;\n    for(key in this._defaultClientOptions){\n        if (!hasOwnProperty.call(options, key)) {\n            mergedOptions[key] = this._defaultClientOptions[key];\n        } else {\n            mergedOptions[key] = options[key];\n        }\n    }\n    this._clientOptions = mergedOptions;\n};\nexports.OAuth.prototype.getOAuthAccessToken = function(oauth_token, oauth_token_secret, oauth_verifier, callback) {\n    var extraParams = {};\n    if (typeof oauth_verifier == \"function\") {\n        callback = oauth_verifier;\n    } else {\n        extraParams.oauth_verifier = oauth_verifier;\n    }\n    this._performSecureRequest(oauth_token, oauth_token_secret, this._clientOptions.accessTokenHttpMethod, this._accessUrl, extraParams, null, null, function(error, data, response) {\n        if (error) callback(error);\n        else {\n            var results = querystring.parse(data);\n            var oauth_access_token = results[\"oauth_token\"];\n            delete results[\"oauth_token\"];\n            var oauth_access_token_secret = results[\"oauth_token_secret\"];\n            delete results[\"oauth_token_secret\"];\n            callback(null, oauth_access_token, oauth_access_token_secret, results);\n        }\n    });\n};\n// Deprecated\nexports.OAuth.prototype.getProtectedResource = function(url, method, oauth_token, oauth_token_secret, callback) {\n    this._performSecureRequest(oauth_token, oauth_token_secret, method, url, null, \"\", null, callback);\n};\nexports.OAuth.prototype[\"delete\"] = function(url, oauth_token, oauth_token_secret, callback) {\n    return this._performSecureRequest(oauth_token, oauth_token_secret, \"DELETE\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype.get = function(url, oauth_token, oauth_token_secret, callback) {\n    return this._performSecureRequest(oauth_token, oauth_token_secret, \"GET\", url, null, \"\", null, callback);\n};\nexports.OAuth.prototype._putOrPost = function(method, url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n    var extra_params = null;\n    if (typeof post_content_type == \"function\") {\n        callback = post_content_type;\n        post_content_type = null;\n    }\n    if (typeof post_body != \"string\" && !Buffer.isBuffer(post_body)) {\n        post_content_type = \"application/x-www-form-urlencoded\";\n        extra_params = post_body;\n        post_body = null;\n    }\n    return this._performSecureRequest(oauth_token, oauth_token_secret, method, url, extra_params, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.put = function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n    return this._putOrPost(\"PUT\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\nexports.OAuth.prototype.post = function(url, oauth_token, oauth_token_secret, post_body, post_content_type, callback) {\n    return this._putOrPost(\"POST\", url, oauth_token, oauth_token_secret, post_body, post_content_type, callback);\n};\n/**\n * Gets a request token from the OAuth provider and passes that information back\n * to the calling code.\n *\n * The callback should expect a function of the following form:\n *\n * function(err, token, token_secret, parsedQueryString) {}\n *\n * This method has optional parameters so can be called in the following 2 ways:\n *\n * 1) Primary use case: Does a basic request with no extra parameters\n *  getOAuthRequestToken( callbackFunction )\n *\n * 2) As above but allows for provision of extra parameters to be sent as part of the query to the server.\n *  getOAuthRequestToken( extraParams, callbackFunction )\n *\n * N.B. This method will HTTP POST verbs by default, if you wish to override this behaviour you will\n * need to provide a requestTokenHttpMethod option when creating the client.\n *\n **/ exports.OAuth.prototype.getOAuthRequestToken = function(extraParams, callback) {\n    if (typeof extraParams == \"function\") {\n        callback = extraParams;\n        extraParams = {};\n    }\n    // Callbacks are 1.0A related\n    if (this._authorize_callback) {\n        extraParams[\"oauth_callback\"] = this._authorize_callback;\n    }\n    this._performSecureRequest(null, null, this._clientOptions.requestTokenHttpMethod, this._requestUrl, extraParams, null, null, function(error, data, response) {\n        if (error) callback(error);\n        else {\n            var results = querystring.parse(data);\n            var oauth_token = results[\"oauth_token\"];\n            var oauth_token_secret = results[\"oauth_token_secret\"];\n            delete results[\"oauth_token\"];\n            delete results[\"oauth_token_secret\"];\n            callback(null, oauth_token, oauth_token_secret, results);\n        }\n    });\n};\nexports.OAuth.prototype.signUrl = function(url, oauth_token, oauth_token_secret, method) {\n    if (method === undefined) {\n        var method = \"GET\";\n    }\n    var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n    var parsedUrl = URL.parse(url, false);\n    var query = \"\";\n    for(var i = 0; i < orderedParameters.length; i++){\n        query += orderedParameters[i][0] + \"=\" + this._encodeData(orderedParameters[i][1]) + \"&\";\n    }\n    query = query.substring(0, query.length - 1);\n    return parsedUrl.protocol + \"//\" + parsedUrl.host + parsedUrl.pathname + \"?\" + query;\n};\nexports.OAuth.prototype.authHeader = function(url, oauth_token, oauth_token_secret, method) {\n    if (method === undefined) {\n        var method = \"GET\";\n    }\n    var orderedParameters = this._prepareParameters(oauth_token, oauth_token_secret, method, url, {});\n    return this._buildAuthorizationHeaders(orderedParameters);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/oauth2.js":
/*!******************************************!*\
  !*** ./node_modules/oauth/lib/oauth2.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("var querystring = __webpack_require__(/*! querystring */ \"querystring\"), crypto = __webpack_require__(/*! crypto */ \"crypto\"), https = __webpack_require__(/*! https */ \"https\"), http = __webpack_require__(/*! http */ \"http\"), URL = __webpack_require__(/*! url */ \"url\"), OAuthUtils = __webpack_require__(/*! ./_utils */ \"(rsc)/./node_modules/oauth/lib/_utils.js\");\nexports.OAuth2 = function(clientId, clientSecret, baseSite, authorizePath, accessTokenPath, customHeaders) {\n    this._clientId = clientId;\n    this._clientSecret = clientSecret;\n    this._baseSite = baseSite;\n    this._authorizeUrl = authorizePath || \"/oauth/authorize\";\n    this._accessTokenUrl = accessTokenPath || \"/oauth/access_token\";\n    this._accessTokenName = \"access_token\";\n    this._authMethod = \"Bearer\";\n    this._customHeaders = customHeaders || {};\n    this._useAuthorizationHeaderForGET = false;\n    //our agent\n    this._agent = undefined;\n};\n// Allows you to set an agent to use instead of the default HTTP or\n// HTTPS agents. Useful when dealing with your own certificates.\nexports.OAuth2.prototype.setAgent = function(agent) {\n    this._agent = agent;\n};\n// This 'hack' method is required for sites that don't use\n// 'access_token' as the name of the access token (for requests).\n// ( http://tools.ietf.org/html/draft-ietf-oauth-v2-16#section-7 )\n// it isn't clear what the correct value should be atm, so allowing\n// for specific (temporary?) override for now.\nexports.OAuth2.prototype.setAccessTokenName = function(name) {\n    this._accessTokenName = name;\n};\n// Sets the authorization method for Authorization header.\n// e.g. Authorization: Bearer <token>  # \"Bearer\" is the authorization method.\nexports.OAuth2.prototype.setAuthMethod = function(authMethod) {\n    this._authMethod = authMethod;\n};\n// If you use the OAuth2 exposed 'get' method (and don't construct your own _request call )\n// this will specify whether to use an 'Authorize' header instead of passing the access_token as a query parameter\nexports.OAuth2.prototype.useAuthorizationHeaderforGET = function(useIt) {\n    this._useAuthorizationHeaderForGET = useIt;\n};\nexports.OAuth2.prototype._getAccessTokenUrl = function() {\n    return this._baseSite + this._accessTokenUrl; /* + \"?\" + querystring.stringify(params); */ \n};\n// Build the authorization header. In particular, build the part after the colon.\n// e.g. Authorization: Bearer <token>  # Build \"Bearer <token>\"\nexports.OAuth2.prototype.buildAuthHeader = function(token) {\n    return this._authMethod + \" \" + token;\n};\nexports.OAuth2.prototype._chooseHttpLibrary = function(parsedUrl) {\n    var http_library = https;\n    // As this is OAUth2, we *assume* https unless told explicitly otherwise.\n    if (parsedUrl.protocol != \"https:\") {\n        http_library = http;\n    }\n    return http_library;\n};\nexports.OAuth2.prototype._request = function(method, url, headers, post_body, access_token, callback) {\n    var parsedUrl = URL.parse(url, true);\n    if (parsedUrl.protocol == \"https:\" && !parsedUrl.port) {\n        parsedUrl.port = 443;\n    }\n    var http_library = this._chooseHttpLibrary(parsedUrl);\n    var realHeaders = {};\n    for(var key in this._customHeaders){\n        realHeaders[key] = this._customHeaders[key];\n    }\n    if (headers) {\n        for(var key in headers){\n            realHeaders[key] = headers[key];\n        }\n    }\n    realHeaders[\"Host\"] = parsedUrl.host;\n    if (!realHeaders[\"User-Agent\"]) {\n        realHeaders[\"User-Agent\"] = \"Node-oauth\";\n    }\n    if (post_body) {\n        if (Buffer.isBuffer(post_body)) {\n            realHeaders[\"Content-Length\"] = post_body.length;\n        } else {\n            realHeaders[\"Content-Length\"] = Buffer.byteLength(post_body);\n        }\n    } else {\n        realHeaders[\"Content-length\"] = 0;\n    }\n    if (access_token && !(\"Authorization\" in realHeaders)) {\n        if (!parsedUrl.query) parsedUrl.query = {};\n        parsedUrl.query[this._accessTokenName] = access_token;\n    }\n    var queryStr = querystring.stringify(parsedUrl.query);\n    if (queryStr) queryStr = \"?\" + queryStr;\n    var options = {\n        host: parsedUrl.hostname,\n        port: parsedUrl.port,\n        path: parsedUrl.pathname + queryStr,\n        method: method,\n        headers: realHeaders\n    };\n    this._executeRequest(http_library, options, post_body, callback);\n};\nexports.OAuth2.prototype._executeRequest = function(http_library, options, post_body, callback) {\n    // Some hosts *cough* google appear to close the connection early / send no content-length header\n    // allow this behaviour.\n    var allowEarlyClose = OAuthUtils.isAnEarlyCloseHost(options.host);\n    var callbackCalled = false;\n    function passBackControl(response, result) {\n        if (!callbackCalled) {\n            callbackCalled = true;\n            if (!(response.statusCode >= 200 && response.statusCode <= 299) && response.statusCode != 301 && response.statusCode != 302) {\n                callback({\n                    statusCode: response.statusCode,\n                    data: result\n                });\n            } else {\n                callback(null, result, response);\n            }\n        }\n    }\n    var result = \"\";\n    //set the agent on the request options\n    if (this._agent) {\n        options.agent = this._agent;\n    }\n    var request = http_library.request(options);\n    request.on(\"response\", function(response) {\n        response.on(\"data\", function(chunk) {\n            result += chunk;\n        });\n        response.on(\"close\", function(err) {\n            if (allowEarlyClose) {\n                passBackControl(response, result);\n            }\n        });\n        response.addListener(\"end\", function() {\n            passBackControl(response, result);\n        });\n    });\n    request.on(\"error\", function(e) {\n        callbackCalled = true;\n        callback(e);\n    });\n    if ((options.method == \"POST\" || options.method == \"PUT\") && post_body) {\n        request.write(post_body);\n    }\n    request.end();\n};\nexports.OAuth2.prototype.getAuthorizeUrl = function(params) {\n    var params = params || {};\n    params[\"client_id\"] = this._clientId;\n    return this._baseSite + this._authorizeUrl + \"?\" + querystring.stringify(params);\n};\nexports.OAuth2.prototype.getOAuthAccessToken = function(code, params, callback) {\n    var params = params || {};\n    params[\"client_id\"] = this._clientId;\n    params[\"client_secret\"] = this._clientSecret;\n    var codeParam = params.grant_type === \"refresh_token\" ? \"refresh_token\" : \"code\";\n    params[codeParam] = code;\n    var post_data = querystring.stringify(params);\n    var post_headers = {\n        \"Content-Type\": \"application/x-www-form-urlencoded\"\n    };\n    this._request(\"POST\", this._getAccessTokenUrl(), post_headers, post_data, null, function(error, data, response) {\n        if (error) callback(error);\n        else {\n            var results;\n            try {\n                // As of http://tools.ietf.org/html/draft-ietf-oauth-v2-07\n                // responses should be in JSON\n                results = JSON.parse(data);\n            } catch (e) {\n                // .... However both Facebook + Github currently use rev05 of the spec\n                // and neither seem to specify a content-type correctly in their response headers :(\n                // clients of these services will suffer a *minor* performance cost of the exception\n                // being thrown\n                results = querystring.parse(data);\n            }\n            var access_token = results[\"access_token\"];\n            var refresh_token = results[\"refresh_token\"];\n            delete results[\"refresh_token\"];\n            callback(null, access_token, refresh_token, results); // callback results =-=\n        }\n    });\n};\n// Deprecated\nexports.OAuth2.prototype.getProtectedResource = function(url, access_token, callback) {\n    this._request(\"GET\", url, {}, \"\", access_token, callback);\n};\nexports.OAuth2.prototype.get = function(url, access_token, callback) {\n    if (this._useAuthorizationHeaderForGET) {\n        var headers = {\n            \"Authorization\": this.buildAuthHeader(access_token)\n        };\n        access_token = null;\n    } else {\n        headers = {};\n    }\n    this._request(\"GET\", url, headers, \"\", access_token, callback);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/oauth2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/oauth/lib/sha1.js":
/*!****************************************!*\
  !*** ./node_modules/oauth/lib/sha1.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*\n * A JavaScript implementation of the Secure Hash Algorithm, SHA-1, as defined\n * in FIPS 180-1\n * Version 2.2 Copyright Paul Johnston 2000 - 2009.\n * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for details.\n */ /*\n * Configurable variables. You may need to tweak these to be compatible with\n * the server-side, but the defaults work in most cases.\n */ var hexcase = 1; /* hex output format. 0 - lowercase; 1 - uppercase        */ \nvar b64pad = \"=\"; /* base-64 pad character. \"=\" for strict RFC compliance   */ \n/*\n * These are the functions you'll usually want to call\n * They take string arguments and return either hex or base-64 encoded strings\n */ function hex_sha1(s) {\n    return rstr2hex(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction b64_sha1(s) {\n    return rstr2b64(rstr_sha1(str2rstr_utf8(s)));\n}\nfunction any_sha1(s, e) {\n    return rstr2any(rstr_sha1(str2rstr_utf8(s)), e);\n}\nfunction hex_hmac_sha1(k, d) {\n    return rstr2hex(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction b64_hmac_sha1(k, d) {\n    return rstr2b64(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)));\n}\nfunction any_hmac_sha1(k, d, e) {\n    return rstr2any(rstr_hmac_sha1(str2rstr_utf8(k), str2rstr_utf8(d)), e);\n}\n/*\n * Perform a simple self-test to see if the VM is working\n */ function sha1_vm_test() {\n    return hex_sha1(\"abc\").toLowerCase() == \"a9993e364706816aba3e25717850c26c9cd0d89d\";\n}\n/*\n * Calculate the SHA1 of a raw string\n */ function rstr_sha1(s) {\n    return binb2rstr(binb_sha1(rstr2binb(s), s.length * 8));\n}\n/*\n * Calculate the HMAC-SHA1 of a key and some data (raw strings)\n */ function rstr_hmac_sha1(key, data) {\n    var bkey = rstr2binb(key);\n    if (bkey.length > 16) bkey = binb_sha1(bkey, key.length * 8);\n    var ipad = Array(16), opad = Array(16);\n    for(var i = 0; i < 16; i++){\n        ipad[i] = bkey[i] ^ 0x36363636;\n        opad[i] = bkey[i] ^ 0x5C5C5C5C;\n    }\n    var hash = binb_sha1(ipad.concat(rstr2binb(data)), 512 + data.length * 8);\n    return binb2rstr(binb_sha1(opad.concat(hash), 512 + 160));\n}\n/*\n * Convert a raw string to a hex string\n */ function rstr2hex(input) {\n    try {\n        hexcase;\n    } catch (e) {\n        hexcase = 0;\n    }\n    var hex_tab = hexcase ? \"0123456789ABCDEF\" : \"0123456789abcdef\";\n    var output = \"\";\n    var x;\n    for(var i = 0; i < input.length; i++){\n        x = input.charCodeAt(i);\n        output += hex_tab.charAt(x >>> 4 & 0x0F) + hex_tab.charAt(x & 0x0F);\n    }\n    return output;\n}\n/*\n * Convert a raw string to a base-64 string\n */ function rstr2b64(input) {\n    try {\n        b64pad;\n    } catch (e) {\n        b64pad = \"\";\n    }\n    var tab = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\n    var output = \"\";\n    var len = input.length;\n    for(var i = 0; i < len; i += 3){\n        var triplet = input.charCodeAt(i) << 16 | (i + 1 < len ? input.charCodeAt(i + 1) << 8 : 0) | (i + 2 < len ? input.charCodeAt(i + 2) : 0);\n        for(var j = 0; j < 4; j++){\n            if (i * 8 + j * 6 > input.length * 8) output += b64pad;\n            else output += tab.charAt(triplet >>> 6 * (3 - j) & 0x3F);\n        }\n    }\n    return output;\n}\n/*\n * Convert a raw string to an arbitrary string encoding\n */ function rstr2any(input, encoding) {\n    var divisor = encoding.length;\n    var remainders = Array();\n    var i, q, x, quotient;\n    /* Convert to an array of 16-bit big-endian values, forming the dividend */ var dividend = Array(Math.ceil(input.length / 2));\n    for(i = 0; i < dividend.length; i++){\n        dividend[i] = input.charCodeAt(i * 2) << 8 | input.charCodeAt(i * 2 + 1);\n    }\n    /*\n   * Repeatedly perform a long division. The binary array forms the dividend,\n   * the length of the encoding is the divisor. Once computed, the quotient\n   * forms the dividend for the next step. We stop when the dividend is zero.\n   * All remainders are stored for later use.\n   */ while(dividend.length > 0){\n        quotient = Array();\n        x = 0;\n        for(i = 0; i < dividend.length; i++){\n            x = (x << 16) + dividend[i];\n            q = Math.floor(x / divisor);\n            x -= q * divisor;\n            if (quotient.length > 0 || q > 0) quotient[quotient.length] = q;\n        }\n        remainders[remainders.length] = x;\n        dividend = quotient;\n    }\n    /* Convert the remainders to the output string */ var output = \"\";\n    for(i = remainders.length - 1; i >= 0; i--)output += encoding.charAt(remainders[i]);\n    /* Append leading zero equivalents */ var full_length = Math.ceil(input.length * 8 / (Math.log(encoding.length) / Math.log(2)));\n    for(i = output.length; i < full_length; i++)output = encoding[0] + output;\n    return output;\n}\n/*\n * Encode a string as utf-8.\n * For efficiency, this assumes the input is valid utf-16.\n */ function str2rstr_utf8(input) {\n    var output = \"\";\n    var i = -1;\n    var x, y;\n    while(++i < input.length){\n        /* Decode utf-16 surrogate pairs */ x = input.charCodeAt(i);\n        y = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;\n        if (0xD800 <= x && x <= 0xDBFF && 0xDC00 <= y && y <= 0xDFFF) {\n            x = 0x10000 + ((x & 0x03FF) << 10) + (y & 0x03FF);\n            i++;\n        }\n        /* Encode output as utf-8 */ if (x <= 0x7F) output += String.fromCharCode(x);\n        else if (x <= 0x7FF) output += String.fromCharCode(0xC0 | x >>> 6 & 0x1F, 0x80 | x & 0x3F);\n        else if (x <= 0xFFFF) output += String.fromCharCode(0xE0 | x >>> 12 & 0x0F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);\n        else if (x <= 0x1FFFFF) output += String.fromCharCode(0xF0 | x >>> 18 & 0x07, 0x80 | x >>> 12 & 0x3F, 0x80 | x >>> 6 & 0x3F, 0x80 | x & 0x3F);\n    }\n    return output;\n}\n/*\n * Encode a string as utf-16\n */ function str2rstr_utf16le(input) {\n    var output = \"\";\n    for(var i = 0; i < input.length; i++)output += String.fromCharCode(input.charCodeAt(i) & 0xFF, input.charCodeAt(i) >>> 8 & 0xFF);\n    return output;\n}\nfunction str2rstr_utf16be(input) {\n    var output = \"\";\n    for(var i = 0; i < input.length; i++)output += String.fromCharCode(input.charCodeAt(i) >>> 8 & 0xFF, input.charCodeAt(i) & 0xFF);\n    return output;\n}\n/*\n * Convert a raw string to an array of big-endian words\n * Characters >255 have their high-byte silently ignored.\n */ function rstr2binb(input) {\n    var output = Array(input.length >> 2);\n    for(var i = 0; i < output.length; i++)output[i] = 0;\n    for(var i = 0; i < input.length * 8; i += 8)output[i >> 5] |= (input.charCodeAt(i / 8) & 0xFF) << 24 - i % 32;\n    return output;\n}\n/*\n * Convert an array of big-endian words to a string\n */ function binb2rstr(input) {\n    var output = \"\";\n    for(var i = 0; i < input.length * 32; i += 8)output += String.fromCharCode(input[i >> 5] >>> 24 - i % 32 & 0xFF);\n    return output;\n}\n/*\n * Calculate the SHA-1 of an array of big-endian words, and a bit length\n */ function binb_sha1(x, len) {\n    /* append padding */ x[len >> 5] |= 0x80 << 24 - len % 32;\n    x[(len + 64 >> 9 << 4) + 15] = len;\n    var w = Array(80);\n    var a = 1732584193;\n    var b = -271733879;\n    var c = -1732584194;\n    var d = 271733878;\n    var e = -1009589776;\n    for(var i = 0; i < x.length; i += 16){\n        var olda = a;\n        var oldb = b;\n        var oldc = c;\n        var oldd = d;\n        var olde = e;\n        for(var j = 0; j < 80; j++){\n            if (j < 16) w[j] = x[i + j];\n            else w[j] = bit_rol(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);\n            var t = safe_add(safe_add(bit_rol(a, 5), sha1_ft(j, b, c, d)), safe_add(safe_add(e, w[j]), sha1_kt(j)));\n            e = d;\n            d = c;\n            c = bit_rol(b, 30);\n            b = a;\n            a = t;\n        }\n        a = safe_add(a, olda);\n        b = safe_add(b, oldb);\n        c = safe_add(c, oldc);\n        d = safe_add(d, oldd);\n        e = safe_add(e, olde);\n    }\n    return Array(a, b, c, d, e);\n}\n/*\n * Perform the appropriate triplet combination function for the current\n * iteration\n */ function sha1_ft(t, b, c, d) {\n    if (t < 20) return b & c | ~b & d;\n    if (t < 40) return b ^ c ^ d;\n    if (t < 60) return b & c | b & d | c & d;\n    return b ^ c ^ d;\n}\n/*\n * Determine the appropriate additive constant for the current iteration\n */ function sha1_kt(t) {\n    return t < 20 ? 1518500249 : t < 40 ? 1859775393 : t < 60 ? -1894007588 : -899497514;\n}\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */ function safe_add(x, y) {\n    var lsw = (x & 0xFFFF) + (y & 0xFFFF);\n    var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n    return msw << 16 | lsw & 0xFFFF;\n}\n/*\n * Bitwise rotate a 32-bit number to the left.\n */ function bit_rol(num, cnt) {\n    return num << cnt | num >>> 32 - cnt;\n}\nexports.HMACSHA1 = function(key, data) {\n    return b64_hmac_sha1(key, data);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/oauth/lib/sha1.js\n");

/***/ })

};
;